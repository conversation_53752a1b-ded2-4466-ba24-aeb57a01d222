# Music Bulk Import Troubleshooting Guide

## Issues Fixed

### 1. "language override unsupported: Hindi" Error

**Problem**: The translation service was receiving "Hindi" as a target language code instead of the proper language code "hi".

**Root Cause**: The `language` field from Excel was being passed to the translation service as a translatable field, but it should be treated as metadata.

**Solution**: Modified the `processSongUpload` function to exclude the `language` field from translation and only translate the actual content fields (title, artist, musicDirector, lyricist, deity).

**Code Changes**:
```javascript
// Before (incorrect)
const translatedData = await translateDataForStore(translatedFields, songData);

// After (correct)
const translatableData = {
  title: songData.title,
  artist: songData.artist,
  musicDirector: songData.musicDirector,
  lyricist: songData.lyricist,
  deity: songData.deity
};
const translatedData = await translateDataForStore(translatedFields, translatableData);
```

### 2. "Drive Link is required" Error

**Problem**: Many rows in the Excel file have empty Drive Link columns.

**Solution**: Ensure your Excel file has valid Google Drive links in the "Drive Link" column for all rows you want to import.

### 3. "Request failed with status code 404" Error

**Problem**: Google Drive files are not publicly accessible or the URLs are invalid.

**Solutions**:
1. **Make files publicly accessible**:
   - Open the file in Google Drive
   - Click "Share" button
   - Change access to "Anyone with the link can view"
   - Copy the sharing link

2. **Use correct URL format**:
   - ✅ `https://drive.google.com/file/d/FILE_ID/view?usp=sharing`
   - ✅ `https://drive.google.com/file/d/FILE_ID`
   - ✅ `FILE_ID` (just the ID)

3. **Improved error handling**: Added better error messages for different scenarios:
   - 404: File not found or not publicly accessible
   - 403: Access denied
   - Timeout: File too large or slow connection

## Excel File Requirements

### Required Columns
- **Track Name**: Song title (required)
- **Drive Link**: Google Drive URL (required)

### Optional Columns
- **ISRC**: International Standard Recording Code
- **Deity**: Associated deity
- **Singer Name**: Artist/Singer name
- **Music Director**: Music director name
- **Lyricist**: Lyricist name
- **Spotify Link from Audio Sheet**: Spotify track URL
- **Language**: Song language (defaults to Hindi)

### Sample Excel Structure
```
| ISRC         | Deity   | Track Name      | Singer Name | Drive Link                                    |
|--------------|---------|-----------------|-------------|-----------------------------------------------|
| INGB22200001 | Krishna | Krishna Vasudeva| Siddharth   | https://drive.google.com/file/d/ABC123/view   |
| INGB22200002 | Ganesha | Ganesha Mantra  | Siddharth   | https://drive.google.com/file/d/DEF456/view   |
```

## Testing Your Setup

### 1. Verify Google Drive Access
Before importing, test each Google Drive link:
1. Open the link in an incognito browser window
2. Ensure you can download the file without logging in
3. If prompted to login, the file is not publicly accessible

### 2. Test with Small Batch
Start with 2-3 songs to verify the process works:
1. Create a small Excel file with 2-3 rows
2. Ensure all Drive Links are working
3. Test the import API
4. Check the results

### 3. Check API Response
The API returns detailed information about success/failure:
```json
{
  "data": {
    "total": 3,
    "successful": 2,
    "failed": 1,
    "errors": [
      "Row 3: Google Drive file not found or not publicly accessible"
    ],
    "songs": [/* Successfully imported songs */]
  }
}
```

## Common Issues and Solutions

### Issue: "Invalid Google Drive URL"
**Solution**: Ensure the URL contains a valid Google Drive file ID.

### Issue: "Access denied to Google Drive file"
**Solution**: 
1. Open Google Drive
2. Right-click the file → Share
3. Change to "Anyone with the link can view"
4. Use the new sharing link

### Issue: "Download timeout"
**Solution**: 
1. Check file size (very large files may timeout)
2. Ensure stable internet connection
3. Try uploading smaller files first

### Issue: "Title is required"
**Solution**: Ensure the "Track Name" column has values for all rows.

### Issue: Translation errors
**Solution**: 
1. Ensure text fields contain valid text
2. Avoid special characters that might break translation
3. Check that the translation service is properly configured

## Best Practices

1. **File Preparation**:
   - Use the provided Excel template
   - Fill all required fields
   - Test Google Drive links manually first

2. **Batch Size**:
   - Start with small batches (5-10 songs)
   - Gradually increase batch size
   - Monitor server resources

3. **Error Handling**:
   - Review error messages carefully
   - Fix issues row by row
   - Re-import failed rows after fixing

4. **Performance**:
   - Ensure stable internet connection
   - Upload during off-peak hours
   - Monitor server logs for issues

## API Usage

### Correct Request Format
```bash
curl -X POST \
  http://localhost:3000/api/music/bulk-import \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -F 'file=@/path/to/music-data.xlsx'
```

Note: The form field name is `file`, not `excelFile`.

### Response Interpretation
- `total`: Total rows processed
- `successful`: Successfully imported songs
- `failed`: Failed imports
- `errors`: Array of specific error messages
- `songs`: Array of successfully created song objects

## Support

If you continue to experience issues:
1. Check server logs for detailed error information
2. Verify all environment variables are set correctly
3. Ensure Google Cloud Translation API is properly configured
4. Test with a minimal Excel file first
