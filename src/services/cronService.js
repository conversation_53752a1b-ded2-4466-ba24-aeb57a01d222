const cron = require('node-cron');
const { expirePendingOrders } = require('../jobs/expireOrders');
const { logOrderEvent, logBookingEvent } = require('../utils/logger');
const { sendBookingReminders } = require('../crons/bookingReminders');
const { sendBroadcastReminders } = require('../crons/broadcastReminders');
const { sendBookingStartedReminders } = require('../crons/bookingStartedReminder');
const { sendRegistrationBookingReminder } = require('../crons/RegistrationBookingReminder');
const { aadharImageCleanup } = require('../crons/aadharCleanUp');
const { expirePendingBookings } = require('../jobs/expireBookings');

/**
 * Initialize all cron jobs
 */
const initCronJobs = () => {
  // Schedule order expiration job to run every hour
  // Cron format: minute hour day-of-month month day-of-week
  // '0 * * * *' = Run at the start of every hour
  cron.schedule('0 * * * *', async () => {
    try {
      logOrderEvent('CRON_JOB_STARTED', {
        job: 'expirePendingOrders',
        timestamp: new Date()
      });
      
      const result = await expirePendingOrders();
      
      logOrderEvent('CRON_JOB_COMPLETED', {
        job: 'expirePendingOrders',
        result,
        timestamp: new Date()
      });
    } catch (error) {
      logOrderEvent('CRON_JOB_ERROR', {
        job: 'expirePendingOrders',
        error: error.message,
        timestamp: new Date()
      });
    }
  });

  // Schedule booking expiration job to run every hour
  // Cron format: minute hour day-of-month month day-of-week
  // '0 * * * *' = Run at the start of every hour
  cron.schedule('0 * * * *', async () => {
    try {
      logBookingEvent('CRON_JOB_STARTED', {
        job: 'expirePendingBookings',
        timestamp: new Date()
      });
      
      const bookingResult = await expirePendingBookings();
      
      logBookingEvent('CRON_JOB_COMPLETED', {
        job: 'expirePendingBookings',
        bookingResult,
        timestamp: new Date()
      });
    } catch (error) {
      logBookingEvent('CRON_JOB_ERROR', {
        job: 'expirePendingBookings',
        error: error.message,
        timestamp: new Date()
      });
    }
  });

  //* Every day at 12:00 AM
  cron.schedule('0 0 * * *', async () => {
    await sendBookingReminders();
    await aadharImageCleanup();
  });

  cron.schedule('0 * * * *', async () => {
    await sendBookingStartedReminders();
  });

  cron.schedule('0 8 * * *', async () => {
    await sendBroadcastReminders();
  });

  cron.schedule('*/5 * * * *', async () => {
    await sendRegistrationBookingReminder();
  });
 
  // You can add more scheduled jobs here
  
  logOrderEvent('CRON_JOBS_INITIALIZED', {
    timestamp: new Date()
  });
};

module.exports = {
  initCronJobs
};
