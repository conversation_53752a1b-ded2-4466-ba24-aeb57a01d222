const Booking = require('../models/Booking');
const PaymentBooking = require('../models/PaymentBooking');
const TransactionBooking = require('../models/TransactionBooking');
const { logOrderEvent } = require('../utils/logger');

const expirePendingBookings = async () => {
  try {
    // Find bookings that are pending for more than 30 minutes
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

    const pendingBookings = await Booking.find({
      status: 'PENDING',
      createdAt: { $lt: thirtyMinutesAgo }
    });

    logOrderEvent('EXPIRE_BOOKINGS_JOB', {
      count: pendingBookings.length,
      timestamp: new Date()
    });

    // Delete Booking, Payment, Transaction by pendingBookings ids
    await PaymentBooking.deleteMany({
      booking: { $in: pendingBookings.map(booking => booking._id) }
    });
    await TransactionBooking.deleteMany({
      booking: { $in: pendingBookings.map(booking => booking._id) }
    });
    await Booking.deleteMany({
      _id: { $in: pendingBookings.map(booking => booking._id) }
    });

    return {
      success: true,
      expiredCount: pendingBookings.length
    };
  } catch (error) {
    logOrderEvent('EXPIRE_BOOKINGS_JOB_ERROR', {
      error: error.message
    });

    throw error;
  }
};

module.exports = {
  expirePendingBookings
};
