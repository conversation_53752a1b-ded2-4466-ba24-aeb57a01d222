const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
  name: JSON,
  location: JSON,
  dateType: {
    type: String,
    enum: [ 'SPECIFIC_DATE', 'DATE_RANGE' ],
    required: true
  },
  specificDate: {
    type: Date,
    required: function () {
      return this.dateType === 'SPECIFIC_DATE';
    }
  },
  dateRange: {
    startDate: {
      type: Date,
      required: function () {
        return this.dateType === 'DATE_RANGE';
      },
      // validate: {
      //   validator: function (startDate) {
      //     if (this.dateType !== 'DATE_RANGE') {
      //       return true;
      //     }
      //     return startDate >= new Date();
      //   },
      //   message: 'Start date must be greater than or equal to current date'
      // }
    },
    endDate: {
      type: Date,
      required: function () {
        return this.dateType === 'DATE_RANGE';
      },
      // validate: {
      //   validator: function (endDate) {
      //     if (this.dateType !== 'DATE_RANGE') {
      //       return true;
      //     }
      //     return endDate > this.dateRange.startDate;
      //   },
      //   message: 'End date must be greater than start date'
      // }
    }
  },
  startTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  },
  endTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  },
  // Default pricing for the entire event (used for SPECIFIC_DATE events)
  pricing: {
    gstPercentage: {
      type: Number,
      required: false,
      min: 0
    },
    individual: {
      type: Number,
      required: function () {
        return this.dateType === 'SPECIFIC_DATE' || !this.datePricing || this.datePricing.length === 0;
      },
      min: 0
    },
    individualTaxablePrice: {
      type: Number,
      required: false,
      min: 0
    },
    individualGstPrice: {
      type: Number,
      required: false,
      min: 0
    },
    couple: {
      type: Number,
      required: function () {
        return this.dateType === 'SPECIFIC_DATE' || !this.datePricing || this.datePricing.length === 0;
      },
      min: 0
    },
    coupleTaxablePrice: {
      type: Number,
      required: false,
      min: 0
    },
    coupleGstPrice: {
      type: Number,
      required: false,
      min: 0
    },
    family: {
      type: Number,
      required: function () {
        return this.dateType === 'SPECIFIC_DATE' || !this.datePricing || this.datePricing.length === 0;
      },
      min: 0
    },
    familyTaxablePrice: {
      type: Number,
      required: false,
      min: 0
    },
    familyGstPrice: {
      type: Number,
      required: false,
      min: 0
    },
  },
  // Date-specific pricing for DATE_RANGE events
  datePricing: [{
    date: {
      type: Date,
      required: true
    },
    pricing: {
      gstPercentage: {
        type: Number,
        required: false,
        min: 0
      },
      individual: {
        type: Number,
        required: true,
        min: 0
      },
      individualTaxablePrice: {
        type: Number,
        required: false,
        min: 0
      },
      individualGstPrice: {
        type: Number,
        required: false,
        min: 0
      },
      couple: {
        type: Number,
        required: true,
        min: 0
      },
      coupleTaxablePrice: {
        type: Number,
        required: false,
        min: 0
      },
      coupleGstPrice: {
        type: Number,
        required: false,
        min: 0
      },
      family: {
        type: Number,
        required: true,
        min: 0
      },
      familyTaxablePrice: {
        type: Number,
        required: false,
        min: 0
      },
      familyGstPrice: {
        type: Number,
        required: false,
        min: 0
      },
    }
  }],
  serviceCode: {
    type: String,
    required: true,
    trim: true,
  },
  totalCapacityPerDay: {
    type: Number,
    required: true,
    min: 1,
    validate: {
      validator: Number.isInteger,
      message: 'Total capacity per day must be an integer'
    }
  },
  description: JSON,
  guidelines: JSON,
  posterImage: {
    type: String,
    required: true,
    trim: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: false
  },
  showOnHomepage: {
    type: Boolean,
    default: false,
    index: true
  },
  status: {
    type: String,
    enum: [ 'ACTIVE', 'INACTIVE' ],
    default: 'ACTIVE'
  },
  sku: {
    type: String,
    trim: true,
  },
  hsn: {
    type: String,
    trim: true,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

// Indexes for efficient querying
eventSchema.index({ dateType: 1 });
eventSchema.index({ specificDate: 1 });
eventSchema.index({ 'dateRange.startDate': 1, 'dateRange.endDate': 1 });
eventSchema.index({ temple: 1 });
eventSchema.index({ status: 1 });

const Event = mongoose.model('Event', eventSchema);

module.exports = Event;
