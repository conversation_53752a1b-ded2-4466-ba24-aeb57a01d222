const mongoose = require('mongoose');
const { billStatusValue, billTypeValue, itemTypeValue } = require('../constants/dbEnums');

const billSchema = new mongoose.Schema({
  // Bill identification
  billDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  billNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  billStatus: {
    type: String,
    enum: Object.values(billStatusValue),
    default: billStatusValue.OVERDUE,
    required: true
  },
  
  // Bill type and source information
  type: {
    type: String,
    enum: Object.values(billTypeValue),
    required: true
  },
  sourceOfSupply: {
    type: String,
    required: true,
    trim: true
  },
  destinationOfSupply: {
    type: String,
    required: true,
    trim: true
  },
  gstIdentificationNumber: {
    type: String,
    required: true,
    trim: true,
    uppercase: true
  },
  vendorName: {
    type: String,
    required: true,
    trim: true
  },
  dueDate: {
    type: Date,
    required: true
  },
  currencyCode: {
    type: String,
    default: 'INR',
    required: true,
    trim: true
  },
  
  // Item details
  itemName: {
    type: String,
    required: true,
    trim: true
  },
  sku: {
    type: String,
    required: true,
    trim: true
  },
  itemDescription: {
    type: String,
    required: false,
    trim: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1,
    default: 1
  },
  rate: {
    type: Number,
    required: true,
    min: 0
  },
  itemType: {
    type: String,
    enum: Object.values(itemTypeValue),
    required: true
  },
  
  // Tax and pricing details
  taxPercentage: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  taxAmount: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  itemTotal: {
    type: Number,
    required: true,
    min: 0
  },
  subTotal: {
    type: Number,
    required: true,
    min: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  balance: {
    type: Number,
    required: true,
    min: 0
  },
  
  // Payment terms
  paymentTermsLabel: {
    type: String,
    required: false,
    trim: true
  },
  
  // Customer details
  customerName: {
    type: String,
    required: true,
    trim: true
  },
  purchaseOrderNumber: {
    type: String,
    required: true,
    trim: true
  },
  
  // Reference IDs (optional based on type)
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: false
  },
  pooja: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PoojaSchedule',
    required: false
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'vendorModel',
    required: false
  },
  vendorModel: {
    type: String,
    enum: [ 'Admin', 'Vendor' ],
    required: false
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: false
  },
  variant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductVariant',
    required: false
  },
  
  // Reference to booking or order
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: false
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: false
  },
  
  // User reference
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  transaction: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'transactionModel',
    required: false
  },
  transactionModel: {
    type: String,
    enum: [ 'TransactionBooking', 'Transaction', 'PayPhiTransaction' ],
    required: false
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false
  }
}, { timestamps: true });

// Indexes for better query performance
billSchema.index({ billNumber: 1 }, { unique: true });
billSchema.index({ type: 1 });
billSchema.index({ billStatus: 1 });
billSchema.index({ billDate: 1 });
billSchema.index({ dueDate: 1 });
billSchema.index({ templeId: 1 });
billSchema.index({ vendorId: 1 });
billSchema.index({ productId: 1 });
billSchema.index({ variantId: 1 });
billSchema.index({ bookingId: 1 });
billSchema.index({ orderId: 1 });
billSchema.index({ userId: 1 });
billSchema.index({ createdAt: 1 });

const Bill = mongoose.model('Bill', billSchema);

module.exports = Bill;
