const mongoose = require('mongoose');

const musicSchema = new mongoose.Schema({
  title: <PERSON><PERSON><PERSON>,
  artist: <PERSON><PERSON><PERSON>,
  musicDirector: <PERSON><PERSON><PERSON>,
  lyricist: <PERSON><PERSON><PERSON>,
  image: {
    type: String,
    required: false,
    trim: true,
  },
  language: {
    type: String,
    default: 'Hindi',
    trim: true,
  },
  duration: {
    type: Number, 
    default: 0,
  },
  song: {
    type: String,
    required: true,
    trim: true,
  },
  lyrics: {
    type: String,
    default: null,
  },
  fileSize: {
    type: Number,
    default: 0,
  },
  isrc: {
    type: String,
    default: null,
  },
  deity: JSON,
  spotifyLink: {
    type: String,
    default: null,
  },
  driveLink: {
    type: String,
    default: null,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
},
{ timestamps: true }
);

const Music = mongoose.model('Music', musicSchema);

module.exports = Music;