const mongoose = require('mongoose');
const { transactionStatus, transactionType, type } = require('../constants/dbEnums');

const transactionBookingSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  paymentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PaymentBooking',
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking'
  },
  type: {
    type: String,
    enum: Object.values(type),
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    enum: Object.values(transactionStatus),
    required: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  transactionType: {
    type: String,
    enum: Object.values(transactionType),
    required: true
  },
  currency: {
    type: String,
    default: 'INR',
    uppercase: true,
    trim: true
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  refundWebhookPayload: {
    type: [ Object ],
    required: false
  }
}, { timestamps: true });

// Indexes
transactionBookingSchema.index({ userId: 1 });
transactionBookingSchema.index({ paymentId: 1 });
transactionBookingSchema.index({ darshanBookingId: 1 });
transactionBookingSchema.index({ poojaBookingId: 1 });
transactionBookingSchema.index({ status: 1 });
transactionBookingSchema.index({ type: 1 });
transactionBookingSchema.index({ createdAt: 1 });

const TransactionBooking = mongoose.model('TransactionBooking', transactionBookingSchema);

module.exports = TransactionBooking;