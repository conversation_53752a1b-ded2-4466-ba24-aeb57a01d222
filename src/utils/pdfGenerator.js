const fs = require('fs');
const path = require('path');
const os = require('os');
const { v4: uuidv4 } = require('uuid');
const AWS = require('aws-sdk');
const htmlPdf = require('html-pdf-node');
const util = require('util');
const readFile = util.promisify(fs.readFile);
const Handlebars = require('handlebars');

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

/**
 * Format date to a readable string
 * @param {Date} date - Date to format
 * @returns {String} Formatted date string
 */
const formatDate = (date) => {
  const d = new Date(date);

  return d.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};

/**
 * Format number to currency
 * @param {Number} num - Number to format
 * @returns {String} Formatted currency string
 */
const formatCurrency = (num) => {
  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num);
};

/**
 * Helper function to format variant attributes into a readable string
 * @param {Map} attributes - Map of variant attributes
 * @returns {String} Formatted attributes string
 */
const formatVariantAttributes = (attributes) => {
  if (!attributes || attributes.size === 0) {
    return '';
  }

  const attributeArray = [];

  attributes.forEach((value, key) => {
    attributeArray.push(`${key}: ${value}`);
  });

  return attributeArray.join(', ');
};

/**
 * Generate an order invoice PDF for a customer
 * @param {Object} orderData - Order data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateTicketDetails = async (ticketDetails) => {

  // Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `ticket-details-${uuidv4()}.pdf`);

  // Read the HTML template
  const templatePath = path.join(__dirname, '../views/bookingTickets.html');
  const templateSource = await readFile(templatePath, 'utf8');

  // Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  // Generate the HTML from the template and data
  const htmlTemplate = template(ticketDetails);

  // Generate PDF from HTML
  const options = {
    format: 'A4',
    printBackground: true,
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  // Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  // Upload the PDF to S3
  const s3Key = `booking-tickets/${ticketDetails.bookingNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  // Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

/**
 * Generate a commission invoice PDF for a shipment
 * @param {Object} invoiceData - Invoice data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateCommissionInvoice = async (invoiceData) => {
  const {
    invoiceNumber,
    invoiceDate,
    vendorDetails,
    platformDetails,
    settlementPeriod,
    totalTaxablePrice,
    totalGstPrice,
    totalAmount,
    orderNumber = 'N/A', // Default value if not provided
    orderItems = [] // Default empty array if not provided
  } = invoiceData;

  // Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `invoice-${uuidv4()}.pdf`);

  // Read the HTML template
  const templatePath = path.join(__dirname, '../views/vendorInvoice.html');
  const templateSource = await readFile(templatePath, 'utf8');

  // Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);
  
  //* Compute Totals 
  const totalCgstAmount = orderItems.reduce((sum, item) => sum + parseFloat(item.cgstPrice || 0), 0);
  const totalSgstAmount = orderItems.reduce((sum, item) => sum + parseFloat(item.sgstPrice || 0), 0);
  const totalQuantity = orderItems.reduce((sum, item) => sum + parseFloat(item.quantity || 0), 0);

  //* Get logo 
  let logoBase64 = '';

  try {
    const logoPath = path.join(__dirname, '../../public/images/logo.png');
    const logoBuffer = await readFile(logoPath);

    logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
  } catch (error) {
    console.log('Error reading logo file:', error);
  }

  // Prepare the data for the template
  const templateData = {
    logo: logoBase64,
    invoiceNumber,
    invoiceDate: formatDate(invoiceDate),
    platformDetails,
    vendorDetails: {
      businessName: vendorDetails.businessName,
      address: vendorDetails.address,
      gstNumber: vendorDetails.gstNumber
    },
    settlementPeriodStart: formatDate(settlementPeriod.startDate),
    settlementPeriodEnd: formatDate(settlementPeriod.endDate),
    orderNumber,
    orderItems: orderItems || [],
    totalTaxablePrice: formatCurrency(totalTaxablePrice),
    totalGstPrice: formatCurrency(totalGstPrice),
    totalAmount: formatCurrency(totalAmount),
    totalCgstAmount: formatCurrency(totalCgstAmount),
    totalSgstAmount: formatCurrency(totalSgstAmount),
    totalQuantity: totalQuantity
  };

  // Generate the HTML from the template and data
  const htmlTemplate = template(templateData);

  // Generate PDF from HTML
  const options = {
    format: 'A4',
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  // Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  // Upload the PDF to S3
  const s3Key = `invoices/${invoiceNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  // Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

/**
 * Generate an order invoice PDF for a customer
 * @param {Object} orderData - Order data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateOrderInvoice = async (orderData) => {
  const {
    orderNumber,
    orderDate,
    customerName,
    billingAddress,
    shippingAddress,
    items,
    subTotal,
    tax,
    shippingCost,
    discountAmount,
    total,
    igstTotal,
    cgstTotal, 
    sgstTotal, 
    paymentMethod,
    paymentStatus
  } = orderData;

  // Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `order-invoice-${uuidv4()}.pdf`);

  // Read the HTML template
  const templatePath = path.join(__dirname, '../views/orderInvoice.html');
  const templateSource = await readFile(templatePath, 'utf8');

  // Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  // Format items to include variant attributes as text
  const formattedItems = items.map(item => ({
    ...item,
    taxablePrice: formatCurrency(item.taxablePrice),
    subtotal: formatCurrency(item.subtotal),
    igstAmount: formatCurrency(item.igstAmount),
    cgstAmount: formatCurrency(item.cgstAmount),
    sgstAmount: formatCurrency(item.sgstAmount),
    variantAttributesText: item.variantAttributes ? formatVariantAttributes(item.variantAttributes) : ''
  }));

  let logoBase64 = '';

  try {
    const logoPath = path.join(__dirname, '../../public/images/logo.png');
    const logoBuffer = await readFile(logoPath);

    logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
  } catch (error) {
    console.log('Error reading logo file:', error);
  }

  // Generate invoice number
  const invoiceNumber = `OG-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  // Prepare the data for the template
  const adminState = process.env.ADMIN_STATE;
  let isInterState = false;
  let isSameCountry = true;

  if (adminState.trim().toLowerCase() !== billingAddress.state.trim().toLowerCase()) {
    isInterState = true;
  }

  if (billingAddress.country.trim().toLowerCase() !== 'india') {
    isSameCountry = false;
  }

  const templateData = {
    invoiceNumber,
    orderNumber,
    orderDate: formatDate(orderDate),
    customerName,
    billingAddress,
    shippingAddress,
    items: formattedItems,
    subTotal: formatCurrency(subTotal),
    cgstTotal: formatCurrency(cgstTotal),
    sgstTotal: formatCurrency(sgstTotal),
    igstTotal: formatCurrency(igstTotal),
    tax: formatCurrency(tax),
    shippingCost: {
      totalWeight: shippingCost.totalWeight,
      baseShipping: formatCurrency(shippingCost.baseShipping),
      gst: formatCurrency(shippingCost.gst),
      totalShipping: formatCurrency(shippingCost.totalShipping)
    },
    discountAmount: discountAmount ? formatCurrency(discountAmount) : null,
    total: formatCurrency(total),
    paymentMethod,
    paymentStatus,
    logo: logoBase64,
    platformDetails: {
      businessName: process.env.ADMIN_BUSINESS_NAME,
      floorNumber: process.env.ADMIN_FLOOR_NUMBER,
      flatNumber: process.env.ADMIN_FLAT_NUMBER,
      address: process.env.ADMIN_ADDRESS,
      street: process.env.ADMIN_STREET,
      locality: process.env.ADMIN_LOCALITY,
      city: process.env.ADMIN_CITY,
      district: process.env.ADMIN_DISTRICT,
      state: process.env.ADMIN_STATE,
      pinCode: process.env.ADMIN_PINCODE,
      gstNumber: process.env.ADMIN_GSTIN, 
      phone: process.env.ADMIN_PHONE,
      email: process.env.ADMIN_EMAIL
    },
    isInterState,
    isSameCountry
  };

  // Generate the HTML from the template and data
  const htmlTemplate = template(templateData);

  // Generate PDF from HTML
  const options = {
    format: 'A4',
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  // Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  // Upload the PDF to S3
  const s3Key = `order-invoices/${orderNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  // Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

/**
 * Generate a booking confirmation PDF
 * @param {Object} bookingData - Booking data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateBookingConfirmationPDF = async (bookingData, bookingType, bookingDates, individualPrice, couplePrice, familyPrice, serviceName, serviceCode, promotionalKit, address, shippingCharges, bookingEntity, invoiceNumber) => {
  //* 1. Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `booking-${uuidv4()}.pdf`);

  //* 2. Read the HTML template
  const templatePath = path.join(__dirname, '../views/bookingInvoice.html');
  const templateSource = await readFile(templatePath, 'utf8');

  //* 3. Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  //* 4. Extract data from the booking object
  const { bookingNumber, temple, timeSlot, primaryDevoteeDetails, otherDevotees = [], offerings = [], promotionalKitCount, offeringsTotal } = bookingData;

  //* 5. Populate offerings with offering names
  const populatedOfferings = await Promise.all(offerings.map(async (offering) => {
    try {
      // Import the Offering model if not already imported
      const Offering = require('../models/Offering');
      const offeringDetails = await Offering.findById(offering.offering);

      return {
        quantity: offering.quantity,
        amount: offering.amount,
        subtotal: offering.subtotal,
        offeringName: offeringDetails ? offeringDetails.name.en : 'Unknown Offering'
      };
    } catch (error) {
      // Error handled silently, returning default value
      return {
        ...offering,
        offeringName: 'Unknown Offering'
      };
    }
  }));

  //* 6. Format date strings to YYYY-MM-DD
  const formatToYMD = (dateStr) => {
    const d = new Date(dateStr);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };
  
  //* 7. Merge pricing data by date
  const mergedPricing = bookingDates.map(dateStr => {
    const date = formatToYMD(dateStr); // convert to 'YYYY-MM-DD' in local time
  
    const findInList = (list) => {
      return list.find(item => {
        const itemDate = formatToYMD(item.date);

        return itemDate === date;
      }) || { price: 0, quantity: 0, subtotal: 0, taxableValue: 0, gstCharges: 0 };
    };

    const individual = findInList(individualPrice);
    const couple = findInList(couplePrice);
    const family = findInList(familyPrice);
    const dayTotal = parseFloat(individual.subtotal || 0) + parseFloat(couple.subtotal || 0) + parseFloat(family.subtotal || 0);
    const totalTaxableValues = parseFloat(individual.taxableValue || 0) + parseFloat(couple.taxableValue || 0) + parseFloat(family.taxableValue || 0);
    const totalGstCharges = parseFloat(individual.gstCharges || 0) + parseFloat(couple.gstCharges || 0) + parseFloat(family.gstCharges || 0);
    const totalCgstCharges = parseFloat(individual.cgstCharges || 0) + parseFloat(couple.cgstCharges || 0) + parseFloat(family.cgstCharges || 0);
    const totalSgstCharges = parseFloat(individual.sgstCharges || 0) + parseFloat(couple.sgstCharges || 0) + parseFloat(family.sgstCharges || 0);

    return {
      date,
      individual,
      couple,
      family,
      dayTotal,
      totalTaxableValues,
      totalCgstCharges,
      totalSgstCharges,
      totalGstCharges,
      totalIgstCharges: totalSgstCharges + totalCgstCharges
    };
  });

  const pricingTotal = mergedPricing.reduce((sum, curr) => sum + curr.dayTotal, 0);

  let logoBase64 = '';

  try {
    const logoPath = path.join(__dirname, '../../public/images/logo.png');
    const logoBuffer = await readFile(logoPath);

    logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
  } catch (error) {
    console.log('Error reading logo file:', error);
  }

  const isSameCountry = ((address.country.en.trim().toLowerCase() || 'india') === 'india');
  const isSameState = address.state.en.trim().toLowerCase() === process.env.ADMIN_STATE.trim().toLowerCase();

  const totalTaxableAmountOfServices = mergedPricing.reduce((sum, curr) => sum + curr.totalTaxableValues, 0);
  
  const discountAmount = bookingData.discountAmount;

  const finalGstCharges = (bookingData.discountAmount) ? ((parseFloat(totalTaxableAmountOfServices) - parseFloat(discountAmount)) * bookingEntity.pricing.gstPercentage) / 100 : mergedPricing.reduce((sum, curr) => sum + curr.totalCgstCharges, 0) + mergedPricing.reduce((sum, curr) => sum + curr.totalSgstCharges, 0);
  const finalCgstCharges = parseFloat(parseFloat(finalGstCharges) / 2);
  const finalSgstCharges = parseFloat(parseFloat(finalGstCharges) / 2);

  //* 8. Prepare the data for the template
  const templateData = {
    bookingId: bookingNumber,
    bookingType,
    templeName: temple.name.en || temple.name,
    bookingDates: bookingDates.join(', '),
    timeSlot: `${timeSlot.startTime} - ${timeSlot.endTime}`,
    numberOfPeople: 1 + otherDevotees.length,
    devoteeName: primaryDevoteeDetails.fullName.en || primaryDevoteeDetails.fullName,
    devoteePhone: primaryDevoteeDetails.phoneNumber,
    otherDevotees,
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS,
    offerings: populatedOfferings,
    offeringsTotal,
    offeringsLength: offerings.length,
    discountAmount: bookingData.discountAmount,
    pricingDetails: mergedPricing,
    pricingTotal,
    logo: logoBase64,
    serviceName,
    serviceCode,
    promotionalKitCount,
    promotionalKitCost: promotionalKit ? promotionalKit.taxablePrice : null,
    pujaBoxServiceCode: promotionalKit ? promotionalKit.productCode : null,
    promotionalKitTaxableValue: promotionalKit ? promotionalKit.taxablePrice * promotionalKitCount : null,
    promotionalKitGstPercentage: promotionalKit ? promotionalKit.gstPercentage : null,
    promotionalKitGstCharges: promotionalKit ? promotionalKit.gstPrice * promotionalKitCount : null,
    promotionalKitTotalAmount: promotionalKit ? promotionalKit.price * promotionalKitCount : null,
    totalTaxableAmount: totalTaxableAmountOfServices,
    totalGstCharges: mergedPricing.reduce((sum, curr) => sum + curr.totalGstCharges, 0) + (promotionalKit ? promotionalKit.gstPrice * promotionalKitCount : 0) + (shippingCharges ? shippingCharges.gstPrice : 0),
    totalCgstCharges: mergedPricing.reduce((sum, curr) => sum + curr.totalCgstCharges, 0),
    totalSgstCharges: mergedPricing.reduce((sum, curr) => sum + curr.totalSgstCharges, 0),
    totalIgstCharges: mergedPricing.reduce((sum, curr) => sum + curr.totalCgstCharges, 0) + mergedPricing.reduce((sum, curr) => sum + curr.totalSgstCharges, 0),
    totalAmount: mergedPricing.reduce((sum, curr) => sum + curr.dayTotal, 0) + (promotionalKit ? promotionalKit.price * promotionalKitCount : 0) + (shippingCharges ? shippingCharges.price : 0),
    finalAmount: bookingData.totalAmount,
    invoiceDate: formatDate(new Date()),
    invoiceNumber,
    templeState: bookingData.temple ? bookingData.temple.state?.en : '',
    templeCity: bookingData.temple ? bookingData.temple.city?.en : '',
    devoteeAddressLine1: address ? (address.addressLine1 && address.addressLine1.en ? address.addressLine1.en : '') : '',
    devoteeAddressLine2: address ? (address.addressLine2 && address.addressLine2.en ? address.addressLine2.en : '') : '',
    devoteeState: address ? address.state?.en : '',
    devoteeCity: address ? address.city?.en : '',
    devoteePostalCode: address ? address.postalCode : '',
    shippingCharges,
    shippingTaxablePrice: shippingCharges ? shippingCharges.taxablePrice : null,
    shippingGstPercentage: shippingCharges ? shippingCharges.gstPercentage : null,
    shippingGstCharges: shippingCharges ? shippingCharges.gstPrice : null,
    shippingTotalAmount: shippingCharges ? shippingCharges.price : null,
    rowSpan: promotionalKitCount || shippingCharges ? 4 : 3,
    isSameCountry,
    isSameState,
    finalGstCharges,
    finalCgstCharges,
    finalSgstCharges,
    isEvent: bookingType === 'Event'
  };
  
  //* 9. Generate the HTML from the template and data
  const htmlTemplate = template(templateData);

  //* 10. Generate PDF from HTML
  const options = {
    format: 'A4',
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  //* 11. Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  //* 12. Upload the PDF to S3
  const s3Key = `booking-confirmations/${bookingNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  //* 13. Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

/**
 * Generate a sample PDF for testing purposes
 * @param {Object} sampleData - Sample data for PDF generation
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateSamplePDF = async (sampleData) => {
  const {
    adminEmail,
    testTitle = 'Sample PDF Test',
    testMessage = 'This is a test PDF generated using html-pdf-node and handlebars.',
    generatedAt = new Date()
  } = sampleData;

  // Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `sample-pdf-${uuidv4()}.pdf`);

  // Read the HTML template
  const templatePath = path.join(__dirname, '../views/sample-pdf-template.html');
  const templateSource = await readFile(templatePath, 'utf8');

  // Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  // Load logo if available
  let logoBase64 = '';

  try {
    const logoPath = path.join(__dirname, '../../public/images/logo.png');
    const logoBuffer = await readFile(logoPath);

    logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
  } catch (error) {
    console.log('Error reading logo file:', error);
  }

  // Prepare the data for the template
  const templateData = {
    testTitle,
    testMessage,
    adminEmail,
    generatedAt: formatDate(generatedAt),
    generatedTime: generatedAt.toLocaleTimeString('en-IN'),
    logo: logoBase64,
    currentYear: new Date().getFullYear(),
    sampleItems: [
      { name: 'Sample Item 1', description: 'This is a sample item for testing', price: formatCurrency(100) },
      { name: 'Sample Item 2', description: 'Another sample item for testing', price: formatCurrency(250) },
      { name: 'Sample Item 3', description: 'Third sample item for testing', price: formatCurrency(150) }
    ],
    totalAmount: formatCurrency(500)
  };

  // Generate the HTML from the template and data
  const htmlTemplate = template(templateData);

  // Generate PDF from HTML
  const options = {
    format: 'A4',
    printBackground: true,
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  // Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  // Upload the PDF to S3
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const s3Key = `sample-pdfs/sample-pdf-${timestamp}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  // Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

module.exports = {
  generateCommissionInvoice,
  generateTicketDetails,
  generateOrderInvoice,
  generateBookingConfirmationPDF,
  generateSamplePDF
};
