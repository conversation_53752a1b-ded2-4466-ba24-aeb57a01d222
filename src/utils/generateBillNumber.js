const Bill = require('../models/Bill');
const states = require('../../data/states.json');

/**
 * Generate bill number for temple services and vendor products
 * Format: BILL-{StateCode}-{SequentialNumber}
 * @param {String} stateSupply - State of supply (temple state or vendor state)
 * @returns {Promise<String>} Generated bill number
 */
const generateBillNumber = async (stateSupply) => {
  try {
    // Find state data from states.json
    const stateData = states.find(
      (s) => s.name.trim().toLowerCase() === stateSupply.trim().toLowerCase()
    );

    if (!stateData) {
      throw new Error(`State not found: ${stateSupply}`);
    }

    const stateId = stateData.id;

    // Find last bill for this state to determine the next number
    const lastBill = await Bill.findOne({
      billNumber: new RegExp(`BILL-${stateId}-`, 'i')
    })
      .sort({ createdAt: -1 })
      .lean();

    let nextNumber = 1;

    if (lastBill?.billNumber) {
      const lastNumber = parseInt(lastBill.billNumber.split('-')[2], 10);

      nextNumber = lastNumber + 1;
    }

    // Format: BILL-{StateCode}-{6-digit-number}
    const billNumber = `BILL-${stateId}-${nextNumber.toString().padStart(6, '0')}`;

    return billNumber;
  } catch (error) {
    console.error('Error generating bill number:', error);
    throw error;
  }
};

module.exports = {
  generateBillNumber
};
