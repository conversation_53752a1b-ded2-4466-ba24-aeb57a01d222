const Booking = require('../models/Booking');
const moment = require('moment');
const { deleteFile } = require('../utils/s3Service'); 
const MEDIA_URL = process.env.MEDIA_URL;

const aadharImageCleanup = async () => {
  try {
    const cutoffDate = moment().subtract(7, 'days').toDate();
    const formattedDate = moment(cutoffDate).format('YYYY-MM-DD');

    const bookings = await Booking.find({
      status: 'COMPLETED',
      $or: [
        // Primary Devotee Dynamic Key
        {
          $and: [
            { [`primaryDevoteeDetails.verification.${formattedDate}.verified`]: true },
            { [`primaryDevoteeDetails.verification.${formattedDate}.verifiedAt`]: { $lte: cutoffDate } },
            { 'primaryDevoteeDetails.aadharImage': { $ne: null } }
          ]
        },
        // Other Devotees Dynamic Key
        {
          otherDevotees: {
            $elemMatch: {
              [`verification.${formattedDate}.verified`]: true,
              [`verification.${formattedDate}.verifiedAt`]: { $lte: cutoffDate },
              aadharImage: { $ne: null }
            }
          }
        }
      ]
    });

    const updates = [];

    for (const booking of bookings) {
      let shouldUpdate = false;
      const updateFields = {};

      // -----------------------
      // Primary Devotee Cleanup
      // -----------------------
      const verificationMap = booking.primaryDevoteeDetails?.verification;
      const primaryVerification = verificationMap instanceof Map ? verificationMap.get(formattedDate) : verificationMap?.[formattedDate];

      if (primaryVerification?.verifiedAt && primaryVerification.verifiedAt < cutoffDate && booking.primaryDevoteeDetails?.aadharImage) {
        const imageUrl = booking.primaryDevoteeDetails.aadharImage;

        if (imageUrl.startsWith(MEDIA_URL)) {
          const key = imageUrl.substring(MEDIA_URL.length + 1);

          try {
            await deleteFile(key);
          } catch (err) {
            console.error('Failed to delete primaryDevotee Aadhaar from S3:', err);
          }
        }

        updateFields['primaryDevoteeDetails.aadharImage'] = null;
        shouldUpdate = true;
      }

      // -----------------------
      // Other Devotees Cleanup
      // -----------------------
      let otherDevoteesUpdated = false;

      const updatedOtherDevotees = booking.otherDevotees?.map(devotee => {
        const odVerification = devotee.verification instanceof Map ? devotee.verification.get(formattedDate) : devotee.verification?.[formattedDate];

        if (odVerification?.verifiedAt && odVerification.verifiedAt < cutoffDate && devotee?.aadharImage) {
          const imageUrl = devotee.aadharImage;

          if (imageUrl.startsWith(MEDIA_URL)) {
            const key = imageUrl.substring(MEDIA_URL.length + 1);

            deleteFile(key).catch(err => {
              console.error('Failed to delete otherDevotee Aadhaar from S3:', err);
            });
          }

          otherDevoteesUpdated = true;

          // Ensure we're returning plain object
          return {
            ...(devotee.toObject?.() || JSON.parse(JSON.stringify(devotee))),
            aadharImage: null
          };
        }

        return devotee;
      });

      if (otherDevoteesUpdated) {
        updateFields.otherDevotees = updatedOtherDevotees;
        shouldUpdate = true;
      }

      // -----------------------
      // DB Update
      // -----------------------
      if (shouldUpdate) {
        updates.push(
          Booking.updateOne(
            { _id: booking._id },
            { $set: updateFields }
          )
        );
      }
    }

    await Promise.all(updates);
  } catch (err) {
    console.error('Error in aadharImageCleanup cron:', err);
    throw err;
  }
};

module.exports = {
  aadharImageCleanup
};
