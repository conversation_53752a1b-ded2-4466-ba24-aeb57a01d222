const Music = require('../../models/Music');
const Language = require('../../models/Language');
const User = require('../../models/User');
const { throwBadRequestError } = require('../../errors');
const { deleteFile } = require('../../utils/s3Service');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');
const XLSX = require('xlsx');
const fs = require('fs');
const axios = require('axios');
const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS S3
const s3 = new AWS.S3({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  signatureVersion: 'v4',
  apiVersion: '2006-03-01'
});

//* 1. Function to save song 
const addSong = async (body, adminId) => {
  // Translate fields
  const translatedFields = [ 'title', 'artist', 'musicDirector', 'lyricist', 'deity' ];
  const translatedData = await translateDataForStore(translatedFields, body);

  translatedFields.forEach(field => {
    if (body[field]) {
      delete body[field];
    }
  });

  // Add song
  const music = await Music.create({
    ...body,
    ...translatedData,
    song: `${process.env.MEDIA_URL}/${body.song}`,
    image: body.image ? `${process.env.MEDIA_URL}/${body.image}` : null,
    lyrics: body.lyrics ? `${process.env.MEDIA_URL}/${body.lyrics}` : null,
    createdBy: adminId
  });

  return music;
};

//* 2. Function to list all the songs 
const listAllSongs = async (query, userId) => {
  const { page, limit, search, language, artist, sortBy = 'createdAt', sortOrder = -1 } = query;
  const skip = (page - 1) & limit;

  let userPreferredLanguage = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        userPreferredLanguage = userLanguage;
      }
    }
  }

  const filters = {};

  //* Filter based on songLanguage 
  if (language) {
    filters.language = language;
  }

  //* Filter based on artist name 
  if (artist) {
    filters[`artist.${userPreferredLanguage.code}`] = { $regex: artist, $options: 'i' };
  }

  //* Search by title or artist 
  if (search) {
    filters.$or = [
      { [`title.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`artist.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`musicDirector.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`lyricist.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`deity.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  const sort = {};

  sort[sortBy] = sortOrder;

  const [ songs, total ] = await Promise.all([
    Music.find(filters)
      .sort(sort)
      .collation()
      .skip(skip)
      .collation({ locale: 'en', strength: 1 })
      .limit(limit)
      .lean(),
    Music.countDocuments(filters)
  ]);

  return {
    songs: await transformTranslatedFields(songs, userPreferredLanguage.code),
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

//* 3. Function to delete song by id 
const deleteSong = async (songId) => {
  const music = await Music.findByIdAndDelete(songId);
  
  if (!music) {
    throwBadRequestError('Song not found');
  }

  //* Delete image from S3
  //* Implement S3 deletion logic here
  if (music.image) {
    try {
      const mediaUrl = process.env.MEDIA_URL;

      if (music.image.startsWith(mediaUrl)) {
        const key = music.image.substring(mediaUrl.length + 1); // +1 for the '/' character

        // Delete the image file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting song image', error);
    }
  }

  if (music.song) {
    try {
      const mediaUrl = process.env.MEDIA_URL;

      if (music.song.startsWith(mediaUrl)) {
        const key = music.song.substring(mediaUrl.length + 1);

        // Delete the song file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting song', error);
    }
  }

  return music;
};

/**
 * Helper function to extract Google Drive file ID from various URL formats
 * @param {string} driveUrl - Google Drive URL
 * @returns {string|null} File ID or null if not found
 */
const extractGoogleDriveFileId = (driveUrl) => {
  if (!driveUrl) {
    return null;
  }

  // Handle different Google Drive URL formats
  const patterns = [
    /\/file\/d\/([a-zA-Z0-9-_]+)/, // /file/d/FILE_ID
    /id=([a-zA-Z0-9-_]+)/, // id=FILE_ID
    /\/d\/([a-zA-Z0-9-_]+)/, // /d/FILE_ID
    /^([a-zA-Z0-9-_]+)$/ // Just the file ID
  ];

  for (const pattern of patterns) {
    const match = driveUrl.match(pattern);

    if (match) {
      return match[1];
    }
  }

  return null;
};

/**
 * Download file from Google Drive and upload to S3
 * @param {string} driveUrl - Google Drive URL
 * @param {string} fileName - Name for the file
 * @param {string} folder - S3 folder (e.g., 'music')
 * @returns {Promise<string>} S3 key
 */
const downloadAndUploadToS3 = async (driveUrl, fileName, folder) => {
  const fileId = extractGoogleDriveFileId(driveUrl);

  if (!fileId) {
    throw new Error(`Invalid Google Drive URL: ${driveUrl}`);
  }

  // Google Drive direct download URL
  const downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;

  try {
    // Download file from Google Drive
    const response = await axios({
      method: 'GET',
      url: downloadUrl,
      responseType: 'stream',
      timeout: 300000, // 5 minutes timeout
      maxRedirects: 5
    });

    // Generate S3 key
    const fileExtension = fileName.split('.').pop() || 'mp3';
    const s3Key = `${folder}/${uuidv4()}.${fileExtension}`;

    // Determine content type
    let contentType = 'audio/mp3';

    if (fileExtension.toLowerCase() === 'mp4') {
      contentType = 'video/mp4';
    } else if ([ 'jpg', 'jpeg', 'png', 'webp' ].includes(fileExtension.toLowerCase())) {
      contentType = `image/${fileExtension.toLowerCase() === 'jpg' ? 'jpeg' : fileExtension.toLowerCase()}`;
    }

    // Upload to S3
    const uploadParams = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: s3Key,
      Body: response.data,
      ContentType: contentType
    };

    await s3.upload(uploadParams).promise();

    return s3Key;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      throw new Error('Google Drive file not found or not publicly accessible. Please check the sharing settings of the file.');
    } else if (error.response && error.response.status === 403) {
      throw new Error('Access denied to Google Drive file. Please ensure the file is publicly accessible.');
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('Download timeout. The file may be too large or connection is slow.');
    } else {
      throw new Error(`Failed to download and upload file: ${error.message}`);
    }
  }
};

/**
 * Process single song row
 * @param {Object} row - Excel row data
 * @param {number} index - Row index for error reporting
 * @param {string} adminId - Admin ID
 * @returns {Promise<Object>} Processing result
 */
const processSingleSong = async (row, index, adminId) => {
  const rowNumber = index + 1;

  try {
    // Map Excel columns to database fields
    const songData = {
      isrc: row['ISRC'] || row['isrc'] || null,
      deity: row['Deity'] || row['deity'] || null,
      title: row['Track Name'] || row['title'] || row['Track_Name'] || null,
      artist: row['Singer Name'] || row['artist'] || row['Singer_Name'] || null,
      musicDirector: row['Music Director'] || row['musicDirector'] || row['Music_Director'] || null,
      lyricist: row['Lyricist'] || row['lyricist'] || null,
      spotifyLink: row['Spotify Link from Audio Sheet'] || row['spotifyLink'] || row['Spotify_Link'] || null,
      driveLink: row['Drive Link'] || row['driveLink'] || row['Drive_Link'] || null,
      language: row['Language'] || row['language'] || 'Hindi'
    };

    // Validate required fields
    if (!songData.title) {
      throw new Error('Title is required');
    }

    if (!songData.driveLink) {
      throw new Error('Drive Link is required');
    }

    // Download and upload song file to S3
    const songS3Key = await downloadAndUploadToS3(
      songData.driveLink,
      `${songData.title}.mp3`,
      'music'
    );

    // Handle translatable fields (exclude language as it's not translatable)
    const translatedFields = [ 'title', 'artist', 'musicDirector', 'lyricist', 'deity' ];
    const translatableData = {
      title: songData.title,
      artist: songData.artist,
      musicDirector: songData.musicDirector,
      lyricist: songData.lyricist,
      deity: songData.deity
    };
    const translatedData = await translateDataForStore(translatedFields, translatableData);

    // Prepare final music data
    const musicData = {
      song: `${process.env.MEDIA_URL}/${songS3Key}`,
      isrc: songData.isrc,
      language: songData.language,
      spotifyLink: songData.spotifyLink,
      driveLink: songData.driveLink,
      createdBy: adminId,
      ...translatedData
    };

    return {
      success: true,
      data: musicData,
      rowNumber
    };

  } catch (error) {
    return {
      success: false,
      error: `Row ${rowNumber}: ${error.message}`,
      rowNumber
    };
  }
};

/**
 * Process and upload songs from Excel file with parallel processing
 * @param {string} filePath - Path to the uploaded Excel file
 * @param {string} adminId - Admin ID who is uploading
 * @param {Object} options - Processing options
 * @param {number} options.batchSize - Custom batch size (optional)
 * @param {boolean} options.enableLogging - Enable progress logging (optional)
 * @returns {Promise<Object>} Upload results
 */
const processSongUpload = async (filePath, adminId, options = {}) => {
  try {
    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    if (!data || data.length === 0) {
      throwBadRequestError('No data found in the uploaded file');
    }

    const results = {
      total: data.length,
      successful: 0,
      failed: 0,
      errors: [],
      songs: []
    };

    // Process songs in parallel batches to avoid overwhelming the system
    // Use custom batch size or calculate optimal batch size based on file size
    const BATCH_SIZE = options.batchSize || (() => {
      if (data.length <= 10) {
        return 5;
      }
      if (data.length <= 50) {
        return 10;
      }
      if (data.length <= 100) {
        return 15;
      }
      return 20; // Maximum recommended batch size
    })();

    const enableLogging = options.enableLogging || false;

    if (enableLogging) {
      // eslint-disable-next-line no-console
      console.log(`Processing ${data.length} songs with batch size: ${BATCH_SIZE}`);
    }
    const batches = [];

    for (let i = 0; i < data.length; i += BATCH_SIZE) {
      batches.push(data.slice(i, i + BATCH_SIZE));
    }

    // Process each batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];

      if (enableLogging) {
        // eslint-disable-next-line no-console
        console.log(`Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} songs)`);
      }

      const batchPromises = batch.map((row, rowIndex) => {
        const globalIndex = batchIndex * BATCH_SIZE + rowIndex;

        return processSingleSong(row, globalIndex, adminId);
      });

      // Wait for all songs in this batch to complete
      const batchResults = await Promise.allSettled(batchPromises);

      // Collect successful songs for bulk database insertion
      const successfulSongs = [];

      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            successfulSongs.push(result.value.data);
          } else {
            results.errors.push(result.value.error);
            results.failed++;
          }
        } else {
          results.errors.push(`Processing error: ${result.reason.message}`);
          results.failed++;
        }
      });

      // Bulk insert successful songs into database
      if (successfulSongs.length > 0) {
        try {
          const insertedSongs = await Music.insertMany(successfulSongs, { ordered: false });

          results.songs.push(...insertedSongs);
          results.successful += insertedSongs.length;
        } catch (error) {
          // Handle partial insertion errors
          if (error.writeErrors) {
            const insertedCount = error.insertedDocs ? error.insertedDocs.length : 0;

            results.successful += insertedCount;
            results.songs.push(...(error.insertedDocs || []));

            error.writeErrors.forEach((writeError) => {
              results.errors.push(`Database insertion error: ${writeError.errmsg}`);
              results.failed++;
            });
          } else {
            results.errors.push(`Batch database insertion failed: ${error.message}`);
            results.failed += successfulSongs.length;
          }
        }
      }
    }

    // Clean up the uploaded Excel file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    return results;

  } catch (error) {
    // Clean up the uploaded Excel file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
};

module.exports = {
  addSong,
  listAllSongs,
  deleteSong,
  processSongUpload,
  processSingleSong
};