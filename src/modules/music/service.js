const Music = require('../../models/Music');
const Language = require('../../models/Language');
const User = require('../../models/User');
const { throwBadRequestError } = require('../../errors');
const { deleteFile } = require('../../utils/s3Service');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');
const XLSX = require('xlsx');
const fs = require('fs');
const axios = require('axios');
const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS S3
const s3 = new AWS.S3({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  signatureVersion: 'v4',
  apiVersion: '2006-03-01'
});

//* 1. Function to save song 
const addSong = async (body, adminId) => {
  // Translate fields
  const translatedFields = [ 'title', 'artist', 'musicDirector', 'lyricist', 'deity' ];
  const translatedData = await translateDataForStore(translatedFields, body);

  translatedFields.forEach(field => {
    if (body[field]) {
      delete body[field];
    }
  });

  // Add song
  const music = await Music.create({
    ...body,
    ...translatedData,
    song: `${process.env.MEDIA_URL}/${body.song}`,
    image: body.image ? `${process.env.MEDIA_URL}/${body.image}` : null,
    lyrics: body.lyrics ? `${process.env.MEDIA_URL}/${body.lyrics}` : null,
    createdBy: adminId
  });

  return music;
};

//* 2. Function to list all the songs 
const listAllSongs = async (query, userId) => {
  const { page, limit, search, language, artist, sortBy = 'createdAt', sortOrder = -1 } = query;
  const skip = (page - 1) & limit;

  let userPreferredLanguage = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        userPreferredLanguage = userLanguage;
      }
    }
  }

  const filters = {};

  //* Filter based on songLanguage 
  if (language) {
    filters.language = language;
  }

  //* Filter based on artist name 
  if (artist) {
    filters[`artist.${userPreferredLanguage.code}`] = { $regex: artist, $options: 'i' };
  }

  //* Search by title or artist 
  if (search) {
    filters.$or = [
      { [`title.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`artist.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`musicDirector.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`lyricist.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`deity.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  const sort = {};

  sort[sortBy] = sortOrder;

  const [ songs, total ] = await Promise.all([
    Music.find(filters)
      .sort(sort)
      .collation()
      .skip(skip)
      .collation({ locale: 'en', strength: 1 })
      .limit(limit)
      .lean(),
    Music.countDocuments(filters)
  ]);

  return {
    songs: await transformTranslatedFields(songs, userPreferredLanguage.code),
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

//* 3. Function to delete song by id 
const deleteSong = async (songId) => {
  const music = await Music.findByIdAndDelete(songId);
  
  if (!music) {
    throwBadRequestError('Song not found');
  }

  //* Delete image from S3
  //* Implement S3 deletion logic here
  if (music.image) {
    try {
      const mediaUrl = process.env.MEDIA_URL;

      if (music.image.startsWith(mediaUrl)) {
        const key = music.image.substring(mediaUrl.length + 1); // +1 for the '/' character

        // Delete the image file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting song image', error);
    }
  }

  if (music.song) {
    try {
      const mediaUrl = process.env.MEDIA_URL;

      if (music.song.startsWith(mediaUrl)) {
        const key = music.song.substring(mediaUrl.length + 1);

        // Delete the song file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting song', error);
    }
  }

  return music;
};

/**
 * Helper function to extract Google Drive file ID from various URL formats
 * @param {string} driveUrl - Google Drive URL
 * @returns {string|null} File ID or null if not found
 */
const extractGoogleDriveFileId = (driveUrl) => {
  if (!driveUrl) {
    return null;
  }

  // Handle different Google Drive URL formats
  const patterns = [
    /\/file\/d\/([a-zA-Z0-9-_]+)/, // /file/d/FILE_ID
    /id=([a-zA-Z0-9-_]+)/, // id=FILE_ID
    /\/d\/([a-zA-Z0-9-_]+)/, // /d/FILE_ID
    /^([a-zA-Z0-9-_]+)$/ // Just the file ID
  ];

  for (const pattern of patterns) {
    const match = driveUrl.match(pattern);

    if (match) {
      return match[1];
    }
  }

  return null;
};

/**
 * Download file from Google Drive and upload to S3
 * @param {string} driveUrl - Google Drive URL
 * @param {string} fileName - Name for the file
 * @param {string} folder - S3 folder (e.g., 'music')
 * @returns {Promise<string>} S3 key
 */
const downloadAndUploadToS3 = async (driveUrl, fileName, folder) => {
  const fileId = extractGoogleDriveFileId(driveUrl);

  if (!fileId) {
    throw new Error(`Invalid Google Drive URL: ${driveUrl}`);
  }

  // Google Drive direct download URL
  const downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;

  try {
    // Download file from Google Drive
    const response = await axios({
      method: 'GET',
      url: downloadUrl,
      responseType: 'stream',
      timeout: 300000, // 5 minutes timeout
      maxRedirects: 5
    });

    // Generate S3 key
    const fileExtension = fileName.split('.').pop() || 'mp3';
    const s3Key = `${folder}/${uuidv4()}.${fileExtension}`;

    // Determine content type
    let contentType = 'audio/mp3';

    if (fileExtension.toLowerCase() === 'mp4') {
      contentType = 'video/mp4';
    } else if ([ 'jpg', 'jpeg', 'png', 'webp' ].includes(fileExtension.toLowerCase())) {
      contentType = `image/${fileExtension.toLowerCase() === 'jpg' ? 'jpeg' : fileExtension.toLowerCase()}`;
    }

    // Upload to S3
    const uploadParams = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: s3Key,
      Body: response.data,
      ContentType: contentType
    };

    await s3.upload(uploadParams).promise();

    return s3Key;
  } catch (error) {
    throw new Error(`Failed to download and upload file: ${error.message}`);
  }
};

/**
 * Process and upload songs from Excel file
 * @param {string} filePath - Path to the uploaded Excel file
 * @param {string} adminId - Admin ID who is uploading
 * @returns {Promise<Object>} Upload results
 */
const processSongUpload = async (filePath, adminId) => {
  try {
    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    if (!data || data.length === 0) {
      throwBadRequestError('No data found in the uploaded file');
    }

    const results = {
      total: data.length,
      successful: 0,
      failed: 0,
      errors: [],
      songs: []
    };

    // Process each row
    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      try {
        // Map Excel columns to database fields
        const songData = {
          isrc: row['ISRC'] || row['isrc'] || null,
          deity: row['Deity'] || row['deity'] || null,
          title: row['Track Name'] || row['title'] || row['Track_Name'] || null,
          artist: row['Singer Name'] || row['artist'] || row['Singer_Name'] || null,
          musicDirector: row['Music Director'] || row['musicDirector'] || row['Music_Director'] || null,
          lyricist: row['Lyricist'] || row['lyricist'] || null,
          spotifyLink: row['Spotify Link from Audio Sheet'] || row['spotifyLink'] || row['Spotify_Link'] || null,
          driveLink: row['Drive Link'] || row['driveLink'] || row['Drive_Link'] || null,
          language: row['Language'] || row['language'] || 'Hindi'
        };

        // Validate required fields
        if (!songData.title) {
          results.errors.push(`Row ${i + 1}: Title is required`);
          results.failed++;
          continue;
        }

        if (!songData.driveLink) {
          results.errors.push(`Row ${i + 1}: Drive Link is required`);
          results.failed++;
          continue;
        }

        // Download and upload song file to S3
        let songS3Key;

        try {
          songS3Key = await downloadAndUploadToS3(
            songData.driveLink,
            `${songData.title}.mp3`,
            `music/${songData.title.toLowerCase()}`
          );
        } catch (error) {
          results.errors.push(`Row ${i + 1}: Failed to download song - ${error.message}`);
          results.failed++;
          continue;
        }

        // Prepare data for database insertion
        const musicData = {
          song: songS3Key,
          isrc: songData.isrc,
          deity: songData.deity,
          language: songData.language,
          spotifyLink: songData.spotifyLink,
          driveLink: songData.driveLink,
          createdBy: adminId
        };

        // Handle translatable fields
        const translatedFields = [ 'title', 'artist', 'musicDirector', 'lyricist', 'deity' ];
        const translatedData = await translateDataForStore(translatedFields, songData);

        // Create the song record
        const music = await Music.create({
          ...musicData,
          ...translatedData,
          song: `${process.env.MEDIA_URL}/${songS3Key}`
        });

        results.songs.push(music);
        results.successful++;

      } catch (error) {
        results.errors.push(`Row ${i + 1}: ${error.message}`);
        results.failed++;
      }
    }

    // Clean up the uploaded Excel file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    return results;

  } catch (error) {
    // Clean up the uploaded Excel file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
};

module.exports = {
  addSong,
  listAllSongs,
  deleteSong,
  processSongUpload
};