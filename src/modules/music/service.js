const Music = require('../../models/Music');
const Language = require('../../models/Language');
const User = require('../../models/User');
const { throwBadRequestError } = require('../../errors');
const { deleteFile } = require('../../utils/s3Service');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');

//* 1. Function to save song 
const addSong = async (body, adminId) => {
  // Translate fields
  const translatedFields = [ 'title', 'artist', 'musicDirector', 'lyricist', 'deity' ];
  const translatedData = await translateDataForStore(translatedFields, body);

  translatedFields.forEach(field => {
    if (body[field]) {
      delete body[field];
    }
  });

  // Add song
  const music = await Music.create({
    ...body,
    ...translatedData,
    song: `${process.env.MEDIA_URL}/${body.song}`,
    image: body.image ? `${process.env.MEDIA_URL}/${body.image}` : null,
    lyrics: body.lyrics ? `${process.env.MEDIA_URL}/${body.lyrics}` : null,
    createdBy: adminId
  });

  return music;
};

//* 2. Function to list all the songs 
const listAllSongs = async (query, userId) => {
  const { page, limit, search, language, artist, sortBy = 'createdAt', sortOrder = -1 } = query;
  const skip = (page - 1) & limit;

  let userPreferredLanguage = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        userPreferredLanguage = userLanguage;
      }
    }
  }

  const filters = {};

  //* Filter based on songLanguage 
  if (language) {
    filters.language = language;
  }

  //* Filter based on artist name 
  if (artist) {
    filters[`artist.${userPreferredLanguage.code}`] = { $regex: artist, $options: 'i' };
  }

  //* Search by title or artist 
  if (search) {
    filters.$or = [
      { [`title.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`artist.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`musicDirector.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`lyricist.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } },
      { [`deity.${userPreferredLanguage.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  const sort = {};

  sort[sortBy] = sortOrder;

  const [ songs, total ] = await Promise.all([
    Music.find(filters)
      .sort(sort)
      .collation()
      .skip(skip)
      .collation({ locale: 'en', strength: 1 })
      .limit(limit)
      .lean(),
    Music.countDocuments(filters)
  ]);

  return {
    songs: await transformTranslatedFields(songs, userPreferredLanguage.code),
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

//* 3. Function to delete song by id 
const deleteSong = async (songId) => {
  const music = await Music.findByIdAndDelete(songId);
  
  if (!music) {
    throwBadRequestError('Song not found');
  }

  //* Delete image from S3
  //* Implement S3 deletion logic here
  if (music.image) {
    try {
      const mediaUrl = process.env.MEDIA_URL;

      if (music.image.startsWith(mediaUrl)) {
        const key = music.image.substring(mediaUrl.length + 1); // +1 for the '/' character

        // Delete the image file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting song image', error);
    }
  }

  if (music.song) {
    try {
      const mediaUrl = process.env.MEDIA_URL;

      if (music.song.startsWith(mediaUrl)) {
        const key = music.song.substring(mediaUrl.length + 1);

        // Delete the song file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting song', error);
    }
  }

  return music;
};

module.exports = {
  addSong,
  listAllSongs,
  deleteSong
};