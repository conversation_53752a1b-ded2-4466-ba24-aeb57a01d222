const express = require('express');
const router = express.Router();
const musicController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin, isUser } = require('../../middleware/roleCheck');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for Excel file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);

    cb(null, 'music-import-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  // Accept only xlsx files
  if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    cb(null, true);
  } else {
    cb(new Error('Only Excel files (.xlsx) are allowed'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

router.post('/upload-url', auth, isAdminOrSuperAdmin, musicController.getUploadUrl);
router.post('/add', auth, isAdminOrSuperAdmin, musicController.addSong);
router.post('/bulk-import', auth, isAdminOrSuperAdmin, upload.single('file'), musicController.bulkImportSongs);
router.get('/list', auth, isUser, musicController.listAllSongs);
router.delete('/delete/:id', auth, isAdminOrSuperAdmin, musicController.deleteSong);

module.exports = router;