const Order = require('../../../models/Order');
const Payment = require('../../../models/Payment');
const Transaction = require('../../../models/Transaction');
const Product = require('../../../models/Product');
const ProductVariant = require('../../../models/ProductVariant');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { orderStatusValue, paymentStatusValue, transactionStatusValue, notificationType } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const crypto = require('crypto');
const { logPaymentEvent } = require('../../../utils/logger');
const { withTransaction, sessionOptions } = require('../../../utils/transactionHelper');
const { validateWebhookSignature } = require('razorpay/dist/utils/razorpay-utils');
const UserDiscount = require('../../../models/UserDiscount');
const Discount = require('../../../models/Discount');
const Cart = require('../../../models/Cart');
const { sendFirebasePushMulticast } = require('../../../utils/firebase_cm');
const Token = require('../../../models/Token');
// const shippingService = require('../../shop/shipping/service');
const path = require('path');
const { sendMail } = require('../../../utils/sendMail');
const moment = require('moment');
const { generateInvoiceForOrder } = require('../order/service');
const { createProductOrderBills } = require('../../admin/bills/service');

/**
 * Verify Razorpay payment
 */
const verifyRazorpayPayment = async ({ userId, orderId, paymentId, signature }) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  const order = await Order.findOne({
    _id: orderId,
    user: userId
  }).populate('user items.product items.variant');

  if (!order) {
    throwBadRequestError(messages.ORDER_NOT_FOUND);
  }

  // Get payment record
  const payment = await Payment.findOne({ order: orderId });

  if (!payment) {
    throwBadRequestError(messages.PAYMENT_RECORD_NOT_FOUND);
  }

  if (!payment.razorpayOrderId) {
    throwBadRequestError(messages.RAZORPAY_ORDER_NOT_CREATED);
  }

  // Verify the signature
  const expectedSignature = crypto.createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
    .update(payment.razorpayOrderId + '|' + paymentId)
    .digest('hex');

  if (expectedSignature !== signature) {
    throwBadRequestError(messages.INVALID_PAYMENT_SIGNATURE);
  }

  // Check current payment status first before starting transaction
  if (payment.status === paymentStatusValue.COMPLETED) {
    // Payment already marked as complete, return success
    logPaymentEvent('VERIFY_ALREADY_PROCESSED', {
      orderId,
      paymentId,
      currentStatus: payment.status
    });
    return { success: true, alreadyProcessed: true };
  }

  // Use transaction helper
  return await withTransaction(async (session) => {
    // Update payment record
    payment.razorpayPaymentId = paymentId;
    payment.razorpaySignature = signature;
    payment.status = paymentStatusValue.COMPLETED;
    await payment.save(sessionOptions(session));

    // Update order status only if it's in PENDING state
    if (order.orderStatus === orderStatusValue.PENDING) {
      order.paymentStatus = paymentStatusValue.COMPLETED;
      order.orderStatus = orderStatusValue.PROCESSING;

      // Now that payment is confirmed, we can reduce the stock
      if (!order.stockReduced && (order.stockUpdates.length > 0 || order.variantStockUpdates.length > 0)) {
        // Update product stock
        if (order.stockUpdates.length > 0) {
          await Product.bulkWrite(order.stockUpdates, sessionOptions(session));
        }

        // Update variant stock
        if (order.variantStockUpdates.length > 0) {
          await ProductVariant.bulkWrite(order.variantStockUpdates, sessionOptions(session));
        }

        // Mark stock as reduced
        order.stockReduced = true;
      }

      await order.save(sessionOptions(session));

      // Create bills for each order item when payment is completed
      try {
        // Get user data if not populated
        let userData = order.user;

        if (!userData || typeof userData === 'string') {
          const User = require('../../../models/User');

          userData = await User.findById(order.user);
        }
        await createProductOrderBills(order, order.items, userData);
        console.log('Bills created successfully for order:', order._id);
      } catch (billError) {
        console.error('Error creating bills for order:', order._id, billError);
        // Don't fail the payment process if bill creation fails
      }

      if (order.discount) {
        const userDiscount = new UserDiscount({
          user: userId,
          discount: order.discount,
          order: order._id
        });

        await userDiscount.save(sessionOptions(session));

        await Discount.findByIdAndUpdate(
          order.discount,
          { $inc: { totalUsageCount: 1 } },
          sessionOptions(session)
        );
      }
      
      const buyNowItem = order.isBuyNow;
      const cart = await Cart.findOne({ user: order.user });

      // Clear cart if this is a cart order (not a buy now order)
      if (!buyNowItem && cart) {
        cart.items = [];
        cart.subtotal = 0;
        cart.lastActive = new Date();
        await cart.save(sessionOptions(session));
      }
    }

    // Update transaction status
    const transaction = await Transaction.findOne({ order: orderId });

    if (transaction && transaction.status === transactionStatusValue.PENDING) {
      transaction.status = transactionStatusValue.COMPLETED;
      transaction.gatewayResponse = { paymentId, signature };
      await transaction.save(sessionOptions(session));
    }

    // Log successful payment verification
    logPaymentEvent('VERIFY_SUCCESS', {
      orderId,
      paymentId,
      newStatus: payment.status
    });

    return { success: true };
  }).catch(error => {
    // Log error
    logPaymentEvent('VERIFY_ERROR', {
      orderId,
      paymentId,
      error: error.message
    });

    throw error;
  });
};

/**
 * Handle Razorpay webhook
 */
const handleRazorpayWebhook = async (webhookData, webhookSignature) => {
  // Log webhook event for debugging

  // For order.paid events, log additional details
  if (webhookData.event === 'order.paid' && webhookData.payload && webhookData.payload.payment && webhookData.payload.payment.entity) {
    const receipt = webhookData.payload.order?.entity?.receipt || '';

    logPaymentEvent('WEBHOOK_RECEIVED', {
      paymentId: webhookData.payload.payment.entity.id,
      orderId: webhookData.payload.payment.entity.order_id,
      status: webhookData.payload.payment.entity.status,
      receipt: receipt,
      isProductOrder: receipt.startsWith('ORD')
    });
  }

  // Verify webhook signature
  const webhookBody = JSON.stringify(webhookData);

  try {
    const isValidSignature = validateWebhookSignature(
      webhookBody,
      webhookSignature,
      process.env.RAZORPAY_WEBHOOK_SECRET
    );

    if (!isValidSignature) {
      throwBadRequestError('Invalid webhook signature');
    }
  } catch (signatureError) {
    throwBadRequestError('Webhook signature validation failed');
  }

  // Check if this is a product order by examining the receipt
  const orderEntity = webhookData.payload.order?.entity;
  const receipt = orderEntity?.receipt || '';
  const isProductOrder = receipt.startsWith('ORD');

  // Log the type of order we're processing
  logPaymentEvent('WEBHOOK_ORDER_TYPE', {
    event: webhookData.event,
    receipt: receipt,
    orderId: orderEntity?.id,
    isProductOrder: isProductOrder
  });

  const paymentEntity = webhookData.payload.payment.entity;
  const razorpayPaymentId = paymentEntity.id;
  const razorpayOrderId = paymentEntity.order_id;

  // Find payment by Razorpay order ID
  const payment = await Payment.findOne({ razorpayOrderId });

  if (!payment) {
    return {
      message: 'Payment booking not found'
    };
  }

  if (webhookData.event === 'order.paid') {
    if (
      webhookData?.payload?.payment?.entity?.status === 'captured'
    ) {
      if (payment.status === paymentStatusValue.COMPLETED) {
        logPaymentEvent('WEBHOOK_ALREADY_PROCESSED', {
          event: webhookData.event,
          razorpayOrderId,
          razorpayPaymentId,
          currentStatus: payment.status
        });
        return { success: true, alreadyProcessed: true };
      }
    } else if (webhookData?.payload?.payment?.entity?.status === 'failed') {
      if (payment.status === paymentStatusValue.COMPLETED || payment.status === paymentStatusValue.REFUNDED) {
        logPaymentEvent('WEBHOOK_PAYMENT_FINAL_STATE', {
          event: webhookData.event,
          razorpayOrderId,
          razorpayPaymentId,
          currentStatus: payment.status
        });
        return { success: true, alreadyProcessed: true };
      }
    }
  }

  // Use transaction helper
  return await withTransaction(async (session) => {
    // Update payment status based on webhook event

    if (webhookData.event === 'order.paid') {
      if (
        webhookData?.payload?.payment?.entity?.status === 'captured'
      ) {
        // Payment successful
        payment.razorpayPaymentId = razorpayPaymentId;
        payment.status = paymentStatusValue.COMPLETED;
        await payment.save(sessionOptions(session));

        // Check if this is a product order
        if (isProductOrder) {
          // Update order status only if it's in a valid state for transition
          const order = await Order.findById(payment.order).populate('user items.product items.variant');

          if (order && (order.orderStatus === orderStatusValue.PENDING || order.paymentStatus !== paymentStatusValue.COMPLETED)) {
            order.paymentStatus = paymentStatusValue.COMPLETED;
            order.orderStatus = orderStatusValue.PROCESSING;

            // Now that payment is confirmed via webhook, we can reduce the stock if not already done
            if (!order.stockReduced && (order.stockUpdates.length > 0 || order.variantStockUpdates.length > 0)) {
              // Update product stock
              // eslint-disable-next-line max-depth
              if (order.stockUpdates.length > 0) {
                await Product.bulkWrite(order.stockUpdates, sessionOptions(session));
              }

              // Update variant stock
              // eslint-disable-next-line max-depth
              if (order.variantStockUpdates.length > 0) {
                await ProductVariant.bulkWrite(order.variantStockUpdates, sessionOptions(session));
              }

              // Mark stock as reduced
              order.stockReduced = true;
            }

            await order.save(sessionOptions(session));

            // Create bills for each order item when payment is completed
            try {
              // Get user data if not populated
              let userData = order.user;

              // eslint-disable-next-line max-depth
              if (!userData || typeof userData === 'string') {
                const User = require('../../../models/User');

                userData = await User.findById(order.user);
              }
              await createProductOrderBills(order, order.items, userData);
              console.log('Bills created successfully for order:', order._id);
            } catch (billError) {
              console.error('Error creating bills for order:', order._id, billError);
              // Don't fail the payment process if bill creation fails
            }

            if (order.discount) {
              const userDiscount = new UserDiscount({
                user: order.user,
                discount: order.discount,
                order: order._id
              });
    
              await userDiscount.save(sessionOptions(session));

              await Discount.findByIdAndUpdate(
                order.discount,
                { $inc: { totalUsageCount: 1 } },
                sessionOptions(session)
              );
            }

            const buyNowItem = order.isBuyNow;
            const cart = await Cart.findOne({ user: order.user });

            // Clear cart if this is a cart order (not a buy now order)
            if (!buyNowItem && cart) {
              cart.items = [];
              cart.subtotal = 0;
              cart.lastActive = new Date();
              await cart.save(sessionOptions(session));
            }

            //* Generate order invoice and send it with email 
            const orderInvoice = await generateInvoiceForOrder(order._id, order.user._id);

            //* Send order placed email to user 
            await sendOrderPlacedEmail(order, orderInvoice);
          }

          const userFCMTokens = await Token.find({ 
            userId: order.user._id,
            fcmToken: { $ne: null, $exists: true }
          }).select('fcmToken');

          const fcmTokens = [ ...new Set(userFCMTokens.map(t => t.fcmToken)) ];

          await sendFirebasePushMulticast(`Your order ${order.orderNumber} has been placed. Track it here.`, '🎁 Order Confirmation', fcmTokens, '', { paymentId: payment._id.toString(), userId: order.user.toString(), orderId: order._id.toString(), type: notificationType.ORDER_CONFIRMED });
        } else {
          // This is not a product order (e.g., booking)
          // For now, just log that we received a non-product order payment
          logPaymentEvent('NON_PRODUCT_ORDER_PAYMENT', {
            event: webhookData.event,
            receipt: receipt,
            razorpayOrderId: razorpayOrderId,
            razorpayPaymentId: razorpayPaymentId
          });

          // In the future, you'll add booking-specific logic here
          // For example: await handleBookingPayment(payment, webhookData);
        }

        // Update transaction status
        const transaction = await Transaction.findOne({ order: payment.order });

        if (transaction && transaction.status !== transactionStatusValue.COMPLETED) {
          transaction.status = transactionStatusValue.COMPLETED;
          transaction.gatewayResponse = paymentEntity;
          await transaction.save(sessionOptions(session));
        }
      } else if (webhookData?.payload?.payment?.entity?.status === 'failed') {
        // Payment failed
        payment.razorpayPaymentId = razorpayPaymentId;
        payment.status = paymentStatusValue.FAILED;
        payment.failureReason = paymentEntity.error_description || 'Payment failed';
        await payment.save(sessionOptions(session));

        // Check if this is a product order
        if (isProductOrder) {
          // Update order status
          const order = await Order.findById(payment.order);

          if (order && order.paymentStatus === paymentStatusValue.PENDING) {
            order.paymentStatus = paymentStatusValue.FAILED;
            await order.save(sessionOptions(session));
          }

          // Update transaction status
          const transaction = await Transaction.findOne({ order: payment.order });

          if (transaction && transaction.status === transactionStatusValue.PENDING) {
            transaction.status = transactionStatusValue.FAILED;
            transaction.gatewayResponse = paymentEntity;
            await transaction.save(sessionOptions(session));
          }
        } else {
          // This is not a product order (e.g., booking)
          // For now, just log that we received a failed non-product order payment
          logPaymentEvent('NON_PRODUCT_ORDER_PAYMENT_FAILED', {
            event: webhookData.event,
            receipt: receipt,
            razorpayOrderId: razorpayOrderId,
            razorpayPaymentId: razorpayPaymentId,
            error: paymentEntity.error_description || 'Payment failed'
          });

          // In the future, you'll add booking-specific logic here for failed payments
          // For example: await handleFailedBookingPayment(payment, webhookData);
        }
      } else {
        logPaymentEvent('WEBHOOK_UNHANDLED_STATUS', {
          event: webhookData.event,
          razorpayOrderId,
          razorpayPaymentId,
          paymentStatus: paymentEntity.status
        });
      }
    }

    // Log webhook processing success
    logPaymentEvent('WEBHOOK_SUCCESS', {
      event: webhookData.event,
      razorpayOrderId,
      razorpayPaymentId,
      newStatus: payment.status
    });

    return { success: true };
  }).catch(error => {
    // Log error
    logPaymentEvent('WEBHOOK_ERROR', {
      event: webhookData.event,
      razorpayOrderId,
      razorpayPaymentId,
      error: error.message
    });

    throw error;
  });
};

//* Funtion to send order placed email to user 
const sendOrderPlacedEmail = async (order, orderInvoice) => {
  const email = order.user.email;
  const templatePath = path.join(__dirname, '../../../views/orderPlaced.html');

  const subject = `Your Ek Ishwar E-Shop Order Has Been Placed – Order ${order.orderNumber}`;
  const data = {
    orderId: order.orderNumber,
    customerName: order.shippingAddress.name,
    orderDate: moment(new Date()).format('DD MMMM YYYY'),
    items: order.items.map((item) => ({
      name: item.name.en,
      quantity: item.quantity
    })),
    shippingAddress: order.shippingAddress,
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS,
    invoiceUrl: orderInvoice.invoiceUrl
  };

  sendMail(email, subject, templatePath, data);
};

module.exports = {
  verifyRazorpayPayment,
  handleRazorpayWebhook
};
