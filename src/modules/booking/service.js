/* eslint-disable max-lines-per-function */
/* eslint-disable max-lines */
const Booking = require('../../models/Booking');
const DarshanSchedule = require('../../models/DarshanSchedule');
const PoojaSchedule = require('../../models/PoojaSchedule');
const Temple = require('../../models/Temple');
const TempleAdmin = require('../../models/TempleAdmin');
const Event = require('../../models/Event');
const Address = require('../../models/Address');
const paymentService = require('../payment/service');
const { throwBadRequestError, throwNotFoundError } = require('../../errors');
const { status, type, userTypeValue, paymentGateway, paymentStatus } = require('../../constants/dbEnums');
const mongoose = require('mongoose');
const KioskUser = require('../../models/KioskUser');
const PoojaRecording = require('../../models/PoojaRecording');
const moment = require('moment');
const { applyDiscount } = require('../../modules/user/discount/service');
const Offering = require('../../models/Offering');
const Discount = require('../../models/Discount');
const User = require('../../models/User');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');
const Language = require('../../models/Language');
const { messages } = require('../../messages');
const PaymentBooking = require('../../models/PaymentBooking');

const validateScheduleDate = (bookingDate, schedule) => {
  // Convert booking date to UTC, removing time part
  const normalizedBookingDate = new Date(bookingDate);

  normalizedBookingDate.setUTCHours(0, 0, 0, 0);

  if (schedule.dateType === 'SPECIFIC_DATE') {
    // Convert schedule date to UTC, removing time part
    const normalizedScheduleDate = new Date(schedule.specificDate);

    normalizedScheduleDate.setUTCHours(0, 0, 0, 0);

    if (normalizedBookingDate.getTime() !== normalizedScheduleDate.getTime()) {
      throwBadRequestError(
        `Invalid date. This schedule is only for ${normalizedScheduleDate.toISOString().split('T')[0]}`
      );
    }
  } else if (schedule.dateType === 'DATE_RANGE') {
    const startDate = new Date(schedule.dateRange.startDate);

    startDate.setUTCHours(0, 0, 0, 0);

    const endDate = new Date(schedule.dateRange.endDate);

    endDate.setUTCHours(23, 59, 59, 999);

    if (normalizedBookingDate < startDate || normalizedBookingDate > endDate) {
      throwBadRequestError(
        `Invalid date. Booking date must be between ${startDate.toISOString().split('T')[0]} and ${endDate.toISOString().split('T')[0]}`
      );
    }
  }
};

//* 1. Function to create darshan booking
const createDarshanBooking = async (bookingData, userId) => {

  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError('User not found');
  }

  if (user.isGuestUser) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'कृपया अपने प्रोफाइल को पूरा करें ताकि आप दर्शन बुक कर सकें।' : 'Please complete your profile to book a darshan');
  }

  //* 1. First check if the temple exists and is active
  const temple = await Temple.findById(bookingData.temple);

  if (!temple) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'मंदिर नहीं मिला या निष्क्रिय है।' : 'Temple not found or inactive');
  }

  //* 2. Get the darshan schedule
  const schedule = await DarshanSchedule.findOne({
    _id: bookingData.darshanSchedule,
    temple: bookingData.temple,
    status: 'ACTIVE'
  });

  if (!schedule) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'दर्शन कार्यक्रम नहीं मिला या निष्क्रिय है।' : 'Darshan schedule not found or inactive');
  }

  //* 3. Validate date based on schedule type
  validateScheduleDate(bookingData.date, schedule);

  //* 4. Validate time slot
  const requestedSlot = schedule.timeSlots.find(slot =>
    slot.startTime === bookingData.timeSlot.startTime &&
    slot.endTime === bookingData.timeSlot.endTime
  );

  if (!requestedSlot) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'अमान्य समय स्लॉट।' : 'Invalid time slot');
  }

  //* 5. Check slot availability
  const existingBookings = await Booking.countDocuments({
    temple: bookingData.temple,
    darshanSchedule: bookingData.darshanSchedule,
    date: bookingData.date,
    'timeSlot.startTime': requestedSlot.startTime,
    'timeSlot.endTime': requestedSlot.endTime,
    status: status.COMPLETED
  });

  const availableOccupancy = Math.max(0, (schedule.occupancyPerSlot || 0) - existingBookings);

  //* 6. Check if slot has enough capacity
  if (availableOccupancy <= 0) {
    throwBadRequestError(
      user.preferredLanguage === 'Hindi' ? 'इस स्लॉट में पर्याप्त क्षमता उपलब्ध नहीं है। 0 स्लॉट उपलब्ध हैं।' : 'Not enough capacity available in this slot. 0 slots available'
    );
  }

  //* 8. Calculate total amount
  let totalAmount = calculateTotalAmount(bookingData, schedule.pricing);

  const receipt = `DARSHAN-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  if (bookingData.primaryDevoteeDetails.aadharImage) {
    bookingData.primaryDevoteeDetails.aadharImage = process.env.MEDIA_URL + '/' + bookingData.primaryDevoteeDetails.aadharImage;
  }

  bookingData.otherDevotees.forEach(devotee => {
    if (devotee.aadharImage) {
      devotee.aadharImage = process.env.MEDIA_URL + '/' + devotee.aadharImage;
    }
  });

  //? Apply discount if provided
  if (bookingData.discount) {
    let totalTaxableAmount = calculateTotalTaxableAmount(bookingData, schedule.pricing);

    const discountData = await Discount.findOne({
      _id: bookingData.discount,
      discountStatus: 'ACTIVE'
    });

    if (!discountData) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'डिस्काउंट नहीं मिला या समाप्त हो गया।' : 'Discount not found or expired');
    }

    const body = {
      discountCode: discountData.code,
      temple: bookingData.temple,
      darshanSchedule: bookingData.darshanSchedule,
      totalAmount
    };

    let result;

    try {
      result = await applyDiscount(userId, body);
    } catch (error) {
      throwBadRequestError(error.message);
    }

    bookingData.discountAmount = parseFloat(result.discountAmount);

    totalTaxableAmount = parseFloat(totalTaxableAmount) - parseFloat(result.discountAmount);

    const gstCharge = parseFloat(parseFloat(totalTaxableAmount * schedule.pricing.gstPercentage) / 100);

    totalAmount = totalTaxableAmount + gstCharge;
  }

  //? Add promotional kit cost if provided
  if (bookingData.promotionalKitCount) {
    if (!schedule.promotionalKit || !schedule.promotionalKit.price) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस बुकिंग के लिए कोई प्रमोशनल किट उपलब्ध नहीं है।' : 'No promotional kit is available for this booking');
    }

    bookingData.promotionalKitCost = parseFloat(schedule.promotionalKit.price);
    bookingData.promotionalKitTotalAmount = parseInt(bookingData.promotionalKitCount) * parseFloat(schedule.promotionalKit.price);
    totalAmount += parseFloat(bookingData.promotionalKitTotalAmount);
  }

  //? Calculate offerings total if provided
  if (bookingData.offerings && bookingData.offerings.length > 0) {
    let total = 0;

    await Promise.all(bookingData.offerings.map(async (offeringData) => {
      const offering = await Offering.findById(offeringData.offering);

      if (!offering || !offering.isActive) {
        throwNotFoundError(user.preferredLanguage === 'Hindi' ? 'ऑफ़रिंग नहीं मिला।' : 'Offering not found');
      }

      if (offering.temple.toString() !== bookingData.temple.toString()) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'ऑफ़रिंग चुने गए मंदिर से संबंधित नहीं है।' : 'Offering does not belong to the selected temple');
      }

      offeringData.amount = parseFloat(offering.amount);
      offeringData.subtotal = parseFloat(offeringData.amount) * parseFloat(offeringData.quantity);
      total += parseFloat(offeringData.subtotal);
    }));

    totalAmount += total;
    bookingData.offeringsTotal = total;
  }

  const translatedData = await translateDataForStore([ 'fullName', 'gotra', 'sankalp', 'fatherOrHusbandName' ], { fullName: bookingData.primaryDevoteeDetails.fullName, gotra: bookingData.primaryDevoteeDetails.gotra, sankalp: bookingData.primaryDevoteeDetails.sankalp, fatherOrHusbandName: bookingData.primaryDevoteeDetails.fatherOrHusbandName });

  bookingData.primaryDevoteeDetails.fullName = translatedData.fullName;
  bookingData.primaryDevoteeDetails.gotra = translatedData.gotra;
  bookingData.primaryDevoteeDetails.sankalp = translatedData.sankalp;
  bookingData.primaryDevoteeDetails.fatherOrHusbandName = translatedData.fatherOrHusbandName;

  await Promise.all(
    bookingData.otherDevotees.map(async (devotee) => {
      const translatedData = await translateDataForStore([ 'fullName', 'gotra' ], { fullName: devotee.fullName, gotra: devotee.gotra });

      devotee.fullName = translatedData.fullName;
      devotee.gotra = translatedData.gotra;
    })
  );

  totalAmount = parseFloat(totalAmount.toFixed(2));

  //* 9. Create the booking if all validations pass
  const booking = await Booking.create({
    ...bookingData,
    bookingNumber: receipt,
    user: userId,
    status: status.PENDING,
    totalAmount
  });

  if (bookingData?.paymentGateway === paymentGateway.ICICI_BANK) {
    console.log('Inside ICICI Booking');

    const mobileNo = (user.countryCode?.replace('+', '') || '91') + user?.phoneNumber;

    const iciciOrder = await paymentService.createICICIOrder({
      amount: totalAmount,
      currency: '356',
      receipt,
      mobileNo,
      email: user?.email,
      bookingId: booking._id,
      userId
    });

    console.log('ICICI Order : ', iciciOrder);

    return {
      booking,
      paymentGateway: paymentGateway.ICICI_BANK,
      payment: {
        tranCtx: iciciOrder.tranCtx,
        amount: totalAmount,
        currency: 'INR',
        iciciKeyId: process.env.ICICI_KEY_ID,
        redirectUrl: `${iciciOrder.redirectUrl}?tranCtx=${iciciOrder.tranCtx}`,
        merchantId: process.env.MERCHANT_ID
      }
    };
  
  } else {
    console.log('Inside Razorpay Booking');

    const { paymentBooking, razorpayOrder } = await paymentService.createRazorpayOrder({
      amount: totalAmount,
      currency: 'INR',
      receipt,
      userId,
      type: type.PHYSICAL_DARSHAN,
      bookingId: booking._id
    });

    return {
      booking,
      paymentGateway: paymentGateway.RAZORPAY,
      payment: {
        orderId: razorpayOrder.id,
        amount: paymentBooking.amount,
        currency: paymentBooking.currency,
        paymentId: paymentBooking._id,
        razorpayKeyId: process.env.RAZORPAY_KEY_ID
      }
    };
  }
};

//* Helper function to calculate total amount
const calculateTotalAmount = (bookingData, pricing) => {
  return (
    (bookingData.individual || 0) * pricing.individual +
    (bookingData.couple || 0) * pricing.couple +
    (bookingData.family || 0) * pricing.family
  );
};

//* Helper function to calculate total taxable amount 
const calculateTotalTaxableAmount = (bookingData, pricing) => {
  return (
    (bookingData.individual || 0) * parseFloat(pricing.individualTaxablePrice) + 
    (bookingData.couple || 0) * parseFloat(pricing.coupleTaxablePrice) + 
    (bookingData.family || 0) * parseFloat(pricing.familyTaxablePrice)  
  );
};

//* Helper function to calculate temple payout amount 
const calculateTemplePayoutAmount = (bookingData, pricing) => {
  const gstPercentage = pricing.gstPercentage || 0;

  const individual = {
    baseAmount: bookingData.individual * parseFloat(pricing.templePayoutIndividualBasePrice || 0),
    gstAmount: bookingData.individual * parseFloat(pricing.templePayoutIndividualGstPrice || 0),
  };

  const couple = {
    baseAmount: bookingData.couple * parseFloat(pricing.templePayoutCoupleBasePrice || 0),
    gstAmount: bookingData.couple * parseFloat(pricing.templePayoutCoupleGstPrice || 0),
  };

  const family = {
    baseAmount: bookingData.family * parseFloat(pricing.templePayoutFamilyBasePrice || 0),
    gstAmount: bookingData.family * parseFloat(pricing.templePayoutFamilyGstPrice || 0),
  };

  individual.totalAmount = individual.baseAmount + individual.gstAmount;
  couple.totalAmount = couple.baseAmount + couple.gstAmount;
  family.totalAmount = family.baseAmount + family.gstAmount;

  const totalBaseAmount = individual.baseAmount + couple.baseAmount + family.baseAmount;
  const totalGstAmount = individual.gstAmount + couple.gstAmount + family.gstAmount;
  const totalAmount = totalBaseAmount + totalGstAmount;

  return {
    templePayoutDetails: {
      gstPercentage,
      individual,
      couple,
      family,
      totalPayoutSummary: {
        totalBaseAmount,
        totalGstAmount,
        totalAmount
      }
    }
  };
};

//* 2. Function to create pooja booking
const createPoojaBooking = async (bookingData, userId) => {

  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError('User not found');
  }

  if (user.isGuestUser) {
    throwBadRequestError('Please complete your profile to book a puja');
  }

  //* 1. First check if the temple exists and is active
  const temple = await Temple.findById(bookingData.temple);

  if (!temple) {
    throwBadRequestError('Temple not found or inactive');
  }

  //* 2. Get the pooja schedule
  const schedule = await PoojaSchedule.findOne({
    _id: bookingData.poojaSchedule,
    temple: bookingData.temple,
    status: 'ACTIVE'
  });

  if (!schedule) {
    throwBadRequestError('Puja schedule not found or inactive');
  }

  //* 3. Validate booking type matches schedule type
  const expectedType = schedule.type === 'VIRTUAL' ? 'VIRTUAL_POOJA' : 'PHYSICAL_POOJA';

  if (bookingData.type !== expectedType) {
    throwBadRequestError(`Invalid booking type. This is a ${schedule.type.toLowerCase()} puja schedule, please use ${expectedType}`);
  }

  // If type = PHYSICAL_POOJA, then throw error if date is before 4 days (excluding today date) :
  if (bookingData.type === 'PHYSICAL_POOJA') {
    const minBookingDateTime = moment().add(5, 'days').format('YYYY-MM-DD');
    const bookingDate = moment(bookingData.date);

    if (bookingDate.isBefore(minBookingDateTime)) {
      throwBadRequestError(`Bookings must be made for ${minBookingDateTime} or later.`);
    }

    // Also add another check that bookings are only allowed before 6 months from minBookingDateTime :
    const maxBookingDateTime = moment().add(6, 'months').format('YYYY-MM-DD');

    if (bookingDate.isAfter(maxBookingDateTime)) {
      throwBadRequestError(`Bookings must be made for dates before ${maxBookingDateTime}`);
    }
  }

  if (bookingData.type === 'VIRTUAL_POOJA') {
    // or virtual pooja, can book minimum 3 hours prior not less than that
    const minBookingDateTime = moment().add(3, 'hours').format('YYYY-MM-DD HH:mm:ss');
    
    // Create date from bookingData.date and bookingData.timeSlot.startTime
    const bookingDate = moment(`${bookingData.date} ${bookingData.timeSlot.startTime}`, 'YYYY-MM-DD HH:mm A');

    if (bookingDate.isBefore(minBookingDateTime)) {
      throwBadRequestError(`Bookings must be made for ${minBookingDateTime} or later.`);
    }

    const maxBookingDateTime = moment().add(6, 'months').format('YYYY-MM-DD');

    if (moment(bookingData.date).isAfter(maxBookingDateTime)) {
      throwBadRequestError(`Bookings must be made for dates before ${maxBookingDateTime}`);
    }
  }

  //* 4. Validate date based on schedule type
  validateScheduleDate(bookingData.date, schedule);

  //* 5. Validate time slot
  const requestedSlot = schedule.timeSlots.find(slot =>
    slot.startTime === bookingData.timeSlot.startTime &&
    slot.endTime === bookingData.timeSlot.endTime
  );

  if (!requestedSlot) {
    throwBadRequestError('Invalid time slot');
  }

  //* 6. Check slot availability
  const existingBookings = await Booking.countDocuments({
    temple: bookingData.temple,
    poojaSchedule: bookingData.poojaSchedule,
    date: bookingData.date,
    'timeSlot.startTime': requestedSlot.startTime,
    'timeSlot.endTime': requestedSlot.endTime,
    status: status.COMPLETED
  });

  const availableOccupancy = Math.max(0, schedule.occupancyPerSlot - existingBookings);

  //* 7. Check if slot has enough capacity
  if (availableOccupancy <= 0) {
    throwBadRequestError(
      'Not enough capacity available in this slot. 0 slots available'
    );
  }

  //* 9. Calculate total amount
  let totalAmount = calculateTotalAmount(bookingData, schedule.pricing);

  const receipt = `PUJA-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  //? Apply discount if provided
  if (bookingData.discount) {
    let totalTaxableAmount = calculateTotalTaxableAmount(bookingData, schedule.pricing);

    const discountData = await Discount.findOne({
      _id: bookingData.discount,
      discountStatus: 'ACTIVE'
    });

    if (!discountData) {
      throwBadRequestError('Discount not found or expired');
    }

    const body = {
      discountCode: discountData.code,
      temple: bookingData.temple,
      poojaSchedule: bookingData.poojaSchedule,
      totalAmount
    };

    let result;

    try {
      result = await applyDiscount(userId, body);
    } catch (error) {
      throwBadRequestError(error.message);
    }

    bookingData.discountAmount = parseFloat(result.discountAmount);
    
    totalTaxableAmount = parseFloat(totalTaxableAmount) - parseFloat(result.discountAmount);

    const gstCharge = parseFloat(parseFloat(totalTaxableAmount * schedule.pricing.gstPercentage) / 100);

    totalAmount = totalTaxableAmount + gstCharge;
  }

  //? Add promotional kit cost if provided
  if (bookingData.type === type.PHYSICAL_POOJA && bookingData.promotionalKitCount) {
    if (!schedule.promotionalKit || !schedule.promotionalKit.price) {
      throwBadRequestError('No promotional kit is available for this booking');
    }

    bookingData.promotionalKitCost = parseFloat(schedule.promotionalKit.price);
    bookingData.promotionalKitTotalAmount = parseInt(bookingData.promotionalKitCount) * parseFloat(schedule.promotionalKit.price);
    totalAmount += parseFloat(bookingData.promotionalKitTotalAmount);
  }

  //? Calculate offerings total if provided
  if (bookingData.offerings && bookingData.offerings.length > 0) {
    let total = 0;

    await Promise.all(bookingData.offerings.map(async (offeringData) => {
      const offering = await Offering.findById(offeringData.offering);

      if (!offering || !offering.isActive) {
        throwNotFoundError('Offering not found');
      }

      if (offering.temple.toString() !== bookingData.temple.toString()) {
        throwBadRequestError('Offering does not belong to the selected temple');
      }

      offeringData.amount = parseFloat(offering.amount);
      offeringData.subtotal = parseFloat(offeringData.amount) * parseFloat(offeringData.quantity);
      total += parseFloat(offeringData.subtotal);
    }));

    totalAmount += total;
    bookingData.offeringsTotal = total;
  }

  if (bookingData.type === type.VIRTUAL_POOJA && schedule.shipping && schedule.shipping.price) {
    totalAmount += parseFloat(schedule.shipping.price);
    bookingData.shippingCharges = parseFloat(schedule.shipping.price);
  }

  const translatedData = await translateDataForStore([ 'fullName', 'gotra', 'sankalp', 'fatherOrHusbandName' ], { fullName: bookingData.primaryDevoteeDetails.fullName, gotra: bookingData.primaryDevoteeDetails.gotra, sankalp: bookingData.primaryDevoteeDetails.sankalp, fatherOrHusbandName: bookingData.primaryDevoteeDetails.fatherOrHusbandName });

  bookingData.primaryDevoteeDetails.fullName = translatedData.fullName;
  bookingData.primaryDevoteeDetails.gotra = translatedData.gotra;
  bookingData.primaryDevoteeDetails.sankalp = translatedData.sankalp;
  bookingData.primaryDevoteeDetails.fatherOrHusbandName = translatedData.fatherOrHusbandName;

  await Promise.all(
    bookingData.otherDevotees.map(async (devotee) => {
      const translatedData = await translateDataForStore([ 'fullName', 'gotra' ], { fullName: devotee.fullName, gotra: devotee.gotra });

      devotee.fullName = translatedData.fullName;
      devotee.gotra = translatedData.gotra;
    })
  );

  totalAmount = parseFloat(totalAmount.toFixed(2));

  //* Calculations for temple payout 
  const { templePayoutDetails } = calculateTemplePayoutAmount(bookingData, schedule.pricing);

  //* 10. Create the booking if all validations pass
  const booking = await Booking.create({
    ...bookingData,
    bookingNumber: receipt,
    user: userId,
    status: status.PENDING,
    totalAmount,
    templePayoutDetails
  });

  let address = '';

  if (bookingData.address) {
    address = await Address.findOne({
      _id: bookingData.address,
      userId: userId
    });
  }

  if (bookingData?.paymentGateway === paymentGateway.ICICI_BANK) {
    console.log('Inside ICICI Pooja Booking');

    const mobileNo = (user.countryCode?.replace('+', '') || '91') + user?.phoneNumber;

    const iciciOrder = await paymentService.createICICIOrder({
      amount: totalAmount,
      currency: '356',
      receipt,
      mobileNo,
      email: user?.email,
      bookingId: booking._id,
      userId
    });

    console.log('ICICI Order : ', iciciOrder);

    return {
      booking,
      address,
      paymentGateway: paymentGateway.ICICI_BANK,
      payment: {
        tranCtx: iciciOrder.tranCtx,
        amount: totalAmount,
        currency: 'INR',
        iciciKeyId: process.env.ICICI_KEY_ID,
        redirectUrl: `${iciciOrder.redirectUrl}?tranCtx=${iciciOrder.tranCtx}`,
        merchantId: process.env.MERCHANT_ID
      }
    };

  } else {
    console.log('Inside Razorpay Pooja Booking');

    const { paymentBooking, razorpayOrder } = await paymentService.createRazorpayOrder({
      amount: totalAmount,
      currency: 'INR',
      receipt,
      userId,
      type: schedule.type === 'VIRTUAL' ? type.VIRTUAL_POOJA : type.PHYSICAL_POOJA,
      bookingId: booking._id
    });

    return {
      booking,
      address,
      paymentGateway: paymentGateway.RAZORPAY,
      payment: {
        orderId: razorpayOrder.id,
        amount: paymentBooking.amount,
        currency: paymentBooking.currency,
        paymentId: paymentBooking._id,
        razorpayKeyId: process.env.RAZORPAY_KEY_ID
      }
    };
  }
};

const cancelBooking = async (bookingId, user) => {

  const booking = await Booking.findOne({
    _id: bookingId,
    user: user.id
  });

  if (!booking) {
    throwBadRequestError('Booking not found');
  }

  // Booking createdAt must be less than 24 hours from now : 

  const bookingDate = moment(booking.createdAt);
  const maxCancellationTime = moment(bookingDate).add(24, 'hours');
  const now = moment();
  
  if (now.isAfter(maxCancellationTime)) {
    throwBadRequestError('Cancellations are only allowed within 24 hours of booking');
  }

  if (booking.type !== type.PHYSICAL_POOJA) {
    throwBadRequestError('Cancellations are only allowed for physical puja bookings');
  }

  if (booking.status !== status.COMPLETED) {
    throwBadRequestError('Only completed bookings can be cancelled');
  }

  const payment = await PaymentBooking.findOne({
    booking: bookingId,
    status: paymentStatus.CAPTURED
  });

  if (!payment) {
    throwBadRequestError('Payment not captured yet. Please try again later or contact support.');
  }
  
  if (booking.paymentGateway === paymentGateway.ICICI_BANK) {
    return booking;
  }

  return await paymentService.refundRazorpayPayment(booking, user, payment);
};

//* 3. Function to create event booking
const createEventBooking = async (bookingData, userId) => {

  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError('User not found');
  }

  if (user.isGuestUser) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'कृपया अपने प्रोफाइल को पूरा करें ताकि आप एक घटना बुक कर सकें।' : 'Please complete your profile to book an event');
  }

  // 1. Find and validate event
  const event = await Event.findOne({
    _id: bookingData.event,
    status: 'ACTIVE'
  });

  if (!event) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'घटना नहीं मिली या निष्क्रिय है।' : 'Event not found or inactive');
  }

  // 4. Validate dates based on event type
  if (event.dateType === 'SPECIFIC_DATE' && bookingData.eventDates.length !== 1) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'कृपया एक अद्यतन तारीख का बुकिंग करें।' : 'Specific date events require exactly one date booking');
  }

  // 5. Check for duplicate dates
  const uniqueDates = new Set(bookingData.eventDates.map(dateObj => dateObj.date));

  if (uniqueDates.size !== bookingData.eventDates.length) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'कृपया अद्यतन तारीख का बुकिंग करें।' : 'Duplicate dates are not allowed in the booking');
  }

  // 6. For DATE_RANGE events, validate that booking dates fall within event's date range
  if (event.dateType === 'DATE_RANGE') {
    const eventStartDate = new Date(event.dateRange.startDate);
    const eventEndDate = new Date(event.dateRange.endDate);

    for (const dateBooking of bookingData.eventDates) {
      const bookingDate = new Date(dateBooking.date);

      if (bookingDate < eventStartDate || bookingDate > eventEndDate) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'अमान्य बुकिंग तिथि' :
          `Invalid booking date ${dateBooking.date}. Event runs from ${event.dateRange.startDate.toISOString().split('T')[0]} to ${event.dateRange.endDate.toISOString().split('T')[0]}`
        );
      }
    }
  }

  // 7. Calculate total amount and validate dates
  let totalAmount = 0;
  const processedDates = [];
  const minBookingDateTime = moment().add(2, 'days').format('YYYY-MM-DD');

  for (const dateBooking of bookingData.eventDates) {
    const bookingDate = moment(dateBooking.date).format('YYYY-MM-DD');

    // 7.1 Check 48-hour advance booking requirement
    if (bookingDate < minBookingDateTime) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'अमान्य बुकिंग तिथि' : `Bookings must be made at least 48 hours in advance. Invalid date: ${dateBooking.date}`);
    }

    // 7.2 Check if date matches event date for SPECIFIC_DATE events
    if (event.dateType === 'SPECIFIC_DATE') {
      const eventDate = moment(event.specificDate).format('YYYY-MM-DD');

      if (bookingDate !== eventDate) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'अमान्य बुकिंग तिथि' : `Invalid date. Event is only on ${event.specificDate.toISOString().split('T')[0]}`);
      }
    }

    // 7.3 Prepare pricing information
    let pricing;

    if (event.dateType === 'SPECIFIC_DATE') {
      pricing = {
        individual: {
          quantity: bookingData.individual || 0,
          price: event.pricing.individual
        },
        couple: {
          quantity: bookingData.couple || 0,
          price: event.pricing.couple
        },
        family: {
          quantity: bookingData.family || 0,
          price: event.pricing.family
        }
      };
    } else {
      // 7.4 For DATE_RANGE, find pricing for the specific date
      const datePricingEntry = event.datePricing.find(
        dp => dp.date.toISOString().split('T')[0] === dateBooking.date.split('T')[0]
      );

      if (!datePricingEntry) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस तिथि के लिए मूल्य निर्धारण नहीं मिला' : `Pricing not found for date ${dateBooking.date}`);
      }

      pricing = {
        individual: {
          quantity: bookingData.individual || 0,
          price: datePricingEntry.pricing.individual
        },
        couple: {
          quantity: bookingData.couple || 0,
          price: datePricingEntry.pricing.couple
        },
        family: {
          quantity: bookingData.family || 0,
          price: datePricingEntry.pricing.family
        }
      };
    }

    // 8. Calculate subtotal
    const subtotal =
      (pricing.individual.quantity * pricing.individual.price) +
      (pricing.couple.quantity * pricing.couple.price) +
      (pricing.family.quantity * pricing.family.price);

    totalAmount += subtotal;

    processedDates.push({
      date: dateBooking.date,
      pricing,
      subtotal
    });
  }

  // 9. Generate booking number
  const receipt = `EVENT-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  //? Apply discount if provided
  if (bookingData.discount) {
    const discountData = await Discount.findOne({
      _id: bookingData.discount,
      discountStatus: 'ACTIVE'
    });

    if (!discountData) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'डिस्काउंट नहीं मिला या अक्टिव नहीं है।' : 'Discount not found or expired');
    }

    const body = {
      discountCode: discountData.code,
      temple: bookingData.temple,
      totalAmount
    };

    let result;

    try {
      result = await applyDiscount(userId, body);
    } catch (error) {
      throwBadRequestError(error.message);
    }

    bookingData.discountAmount = parseFloat(result.discountAmount);
    totalAmount = parseFloat(totalAmount) - parseFloat(result.discountAmount);
  }

  //? Calculate offerings total if provided
  if (bookingData.offerings && bookingData.offerings.length > 0) {
    let total = 0;

    await Promise.all(bookingData.offerings.map(async (offeringData) => {
      const offering = await Offering.findById(offeringData.offering);

      if (!offering || !offering.isActive) {
        throwNotFoundError(user.preferredLanguage === 'Hindi' ? 'ऑफ़रिंग नहीं मिला या अक्टिव नहीं है।' : 'Offering not found');
      }

      if (offering.temple.toString() !== bookingData.temple.toString()) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'ऑफ़रिंग चुनिने वाले मंदिर से नहीं है।' : 'Offering does not belong to the selected temple');
      }

      offeringData.amount = parseFloat(offering.amount);
      offeringData.subtotal = parseFloat(offeringData.amount) * parseFloat(offeringData.quantity);
      total += parseFloat(offeringData.subtotal);
    }));

    totalAmount += total;
    bookingData.offeringsTotal = total;
  }

  const translatedData = await translateDataForStore([ 'fullName', 'gotra', 'sankalp', 'fatherOrHusbandName' ], { fullName: bookingData.primaryDevoteeDetails.fullName, gotra: bookingData.primaryDevoteeDetails.gotra, sankalp: bookingData.primaryDevoteeDetails.sankalp, fatherOrHusbandName: bookingData.primaryDevoteeDetails.fatherOrHusbandName });

  bookingData.primaryDevoteeDetails.fullName = translatedData.fullName;
  bookingData.primaryDevoteeDetails.gotra = translatedData.gotra;
  bookingData.primaryDevoteeDetails.sankalp = translatedData.sankalp;
  bookingData.primaryDevoteeDetails.fatherOrHusbandName = translatedData.fatherOrHusbandName;

  await Promise.all(
    bookingData.otherDevotees.map(async (devotee) => {
      const translatedData = await translateDataForStore([ 'fullName', 'gotra' ], { fullName: devotee.fullName, gotra: devotee.gotra });

      devotee.fullName = translatedData.fullName;
      devotee.gotra = translatedData.gotra;
    })
  );

  totalAmount = parseFloat(totalAmount.toFixed(2));

  // 10. Create the booking
  const booking = await Booking.create({
    ...bookingData,
    bookingNumber: receipt,
    user: userId,
    status: status.PENDING,
    totalAmount,
    eventDates: processedDates,
    timeSlot: {
      startTime: event.startTime,
      endTime: event.endTime
    },
    eventDateType: event.dateType,
  });

  if (bookingData?.paymentGateway === paymentGateway.ICICI_BANK) {
    console.log('Inside ICICI Event Booking');

    const mobileNo = (user.countryCode?.replace('+', '') || '91') + user?.phoneNumber;

    const iciciOrder = await paymentService.createICICIOrder({
      amount: totalAmount,
      currency: '356',
      receipt,
      mobileNo,
      email: user?.email,
      bookingId: booking._id,
      userId
    });

    console.log('ICICI Order : ', iciciOrder);

    return {
      booking,
      paymentGateway: paymentGateway.ICICI_BANK,
      payment: {
        tranCtx: iciciOrder.tranCtx,
        amount: totalAmount,
        currency: 'INR',
        iciciKeyId: process.env.ICICI_KEY_ID,
        redirectUrl: `${iciciOrder.redirectUrl}?tranCtx=${iciciOrder.tranCtx}`,
        merchantId: process.env.MERCHANT_ID
      }
    };

  } else {
    console.log('Inside Razorpay Event Booking');

    const { paymentBooking, razorpayOrder } = await paymentService.createRazorpayOrder({
      amount: totalAmount,
      currency: 'INR',
      receipt,
      userId,
      type: type.EVENT,
      bookingId: booking._id
    });

    return {
      booking,
      paymentGateway: paymentGateway.RAZORPAY,
      payment: {
        orderId: razorpayOrder.id,
        amount: paymentBooking.amount,
        currency: paymentBooking.currency,
        paymentId: paymentBooking._id,
        razorpayKeyId: process.env.RAZORPAY_KEY_ID
      }
    };
  }
};

//* 4. Function to get my bookings
const getMyBookings = async (userId, queryParams) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, status, startDate, endDate, search, type, bookingType } = queryParams;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  // Build base filter object
  const filter = { user: new mongoose.Types.ObjectId(userId) };

  // Add status filter if provided
  if (status) {
    const statusArray = status.split(',').map(s => s.trim().toUpperCase());
    
    filter.status = { $in: statusArray };
  }

  // Add type filter if provided
  if (type) {
    filter.type = type;
  } else {
    filter.type = { $ne: 'EVENT' };
  }

  if (bookingType) {
    const currentDateTime = new Date();
    const currentDate = currentDateTime.toISOString().split('T')[0];
    const currentMinutes = currentDateTime.getHours() * 60 + currentDateTime.getMinutes();

    // Helper expression to convert "hh:mm AM/PM" → minutes since midnight
    const timeInMinutesExpr = {
      $add: [
        {
          $multiply: [
            {
              $cond: [
              // If PM and not 12 PM → hours + 12
                {
                  $and: [
                    { $regexMatch: { input: '$timeSlot.startTime', regex: /PM$/ } },
                    { $not: { $regexMatch: { input: '$timeSlot.startTime', regex: /^12/ } } }
                  ]
                },
                { $add: [
                  { $toInt: { $arrayElemAt: [{ $split: [ '$timeSlot.startTime', ':' ] }, 0 ] } },
                  12
                ]
                },
                {
                // If 12 AM → 0 hours
                  $cond: [
                    { $regexMatch: { input: '$timeSlot.startTime', regex: /^12/ } },
                    0,
                    { $toInt: { $arrayElemAt: [{ $split: [ '$timeSlot.startTime', ':' ] }, 0 ] } }
                  ]
                }
              ]
            },
            60
          ]
        },
        // Add minutes
        {
          $toInt: {
            $arrayElemAt: [
              { $split: [
                { $arrayElemAt: [{ $split: [ '$timeSlot.startTime', ':' ] }, 1 ] },
                ' '
              ]
              },
              0
            ]
          }
        }
      ]
    };

    if (bookingType === 'UPCOMING') {
      if (type === 'EVENT') {
        filter.$or = [
        // Future event dates
          { 'eventDates.date': { $gt: new Date(currentDate) } },
          // Same-day events with future time
          {
            'eventDates.date': new Date(currentDate),
            $expr: { $gte: [ timeInMinutesExpr, currentMinutes ] }
          }
        ];
      } else {
        filter.$or = [
        // Future darshan/pooja dates
          { date: { $gt: new Date(currentDate) } },
          // Same-day with future time slots
          {
            date: new Date(currentDate),
            $expr: { $gte: [ timeInMinutesExpr, currentMinutes ] }
          }
        ];
      }
    } else {
      if (type === 'EVENT') {
        filter.$or = [
        // Past event dates
          { 'eventDates.date': { $lt: new Date(currentDate) } },
          // Same-day events with past time
          {
            'eventDates.date': new Date(currentDate),
            $expr: { $lt: [ timeInMinutesExpr, currentMinutes ] }
          }
        ];
      } else {
        filter.$or = [
        // Past darshan/pooja dates
          { date: { $lt: new Date(currentDate) } },
          // Same-day with past time slots
          {
            date: new Date(currentDate),
            $expr: { $lt: [ timeInMinutesExpr, currentMinutes ] }
          }
        ];
      }
    }
  } else if (startDate || endDate) {
    const dateFilter = {};

    if (startDate) {
      const start = new Date(startDate);

      start.setUTCHours(0, 0, 0, 0);
      dateFilter.$gte = start;
    }
    if (endDate) {
      const end = new Date(endDate);

      end.setUTCHours(23, 59, 59, 999);
      dateFilter.$lte = end;
    }

    filter.$or = [
    // Darshan/pooja
      { date: dateFilter },
      // Events
      {
        eventDates: { $elemMatch: { date: dateFilter } }
      }
    ];
  }

  // Build aggregation pipeline
  const aggregatePipeline = [
    // Match initial filters
    { $match: filter },

    // Lookup temple details
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'temple'
      }
    },
    { $unwind: '$temple' },

    // Lookup darshan schedule details
    {
      $lookup: {
        from: 'darshanschedules',
        localField: 'darshanSchedule',
        foreignField: '_id',
        as: 'darshanSchedule'
      }
    },
    {
      $unwind: {
        path: '$darshanSchedule',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup pooja schedule details
    {
      $lookup: {
        from: 'poojaschedules',
        localField: 'poojaSchedule',
        foreignField: '_id',
        as: 'poojaSchedule'
      }
    },
    {
      $unwind: {
        path: '$poojaSchedule',
        preserveNullAndEmptyArrays: true
      }
    },

    {
      $lookup: {
        from: 'pujaris',
        localField: 'assignedPujari',
        foreignField: '_id',
        as: 'pujari'
      }
    },
    {
      $unwind: {
        path: '$pujari',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup event details
    {
      $lookup: {
        from: 'events',
        localField: 'event',
        foreignField: '_id',
        as: 'event'
      }
    },
    {
      $unwind: {
        path: '$event',
        preserveNullAndEmptyArrays: true
      }
    },

    {
      $lookup: {
        from: 'poojarecordings',
        localField: '_id',
        foreignField: 'booking',
        as: 'poojaRecording'
      }
    },
    {
      $unwind: {
        path: '$poojaRecording',
        preserveNullAndEmptyArrays: true
      }
    },

    // Add search conditions if search parameter is provided
    ...(search ? [{
      $match: {
        $or: [
          { [`temple.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { bookingNumber: { $regex: search, $options: 'i' } },
          { [`primaryDevoteeDetails.fullName.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`darshanSchedule.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`poojaSchedule.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`poojaSchedule.description.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`event.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`pujari.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`darshanSchedule.description.${language.code}`]: { $regex: search, $options: 'i' } }
        ]
      }
    }] : [])
  ];

  // Add sorting
  const sortObject = {};

  // Add computed fields for new sorting options
  aggregatePipeline.push({
    $addFields: {
      // For booking name sorting (pooja schedule name, darshan name, or event name)
      bookingName: {
        $cond: [
          { $eq: [ '$type', 'PHYSICAL_POOJA' ] },
          '$poojaSchedule.name',
          {
            $cond: [
              { $eq: [ '$type', 'VIRTUAL_POOJA' ] },
              '$poojaSchedule.name',
              {
                $cond: [
                  { $eq: [ '$type', 'PHYSICAL_DARSHAN' ] },
                  '$darshanSchedule.name',
                  '$event.name' // For EVENT type
                ]
              }
            ]
          }
        ]
      },
      // For date sorting, we need to handle EVENT and non-EVENT bookings differently
      sortDate: {
        $cond: {
          if: { $eq: [ '$type', 'EVENT' ] },
          then: {
            $cond: {
              if: { $eq: [ parseInt(sortOrder), 1 ] },
              // For ascending order, get the earliest date
              then: { $min: '$eventDates.date' },
              // For descending order, get the latest date
              else: { $max: '$eventDates.date' }
            }
          },
          else: '$date'
        }
      }
    }
  });

  // Set the sort object based on sortBy parameter
  if (sortBy === 'date') {
    sortObject.sortDate = parseInt(sortOrder) || -1;
  } else if (sortBy === 'bookingName') {
    sortObject.bookingName = parseInt(sortOrder) || -1;
  } else if (sortBy === 'type') {
    sortObject.type = parseInt(sortOrder) || -1;
  } else if (sortBy === 'pujari') {
    sortObject[`pujari.name.${language.code}`] = parseInt(sortOrder) || -1;
  } else {
    sortObject[sortBy] = parseInt(sortOrder) || -1;
  }

  aggregatePipeline.push({ $sort: sortObject });

  // Remove temporary fields after sorting
  aggregatePipeline.push({
    $project: {
      sortDate: 0,
      bookingName: 0
    }
  });

  // Add pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  aggregatePipeline.push(
    { $skip: skip },
    { $limit: parseInt(limit) }
  );

  // Execute aggregation
  const bookings = await Booking.aggregate(aggregatePipeline);

  // Get total count for pagination
  const countPipeline = aggregatePipeline.slice(0, -2); // Remove skip and limit
  const totalDocs = await Booking.aggregate([
    ...countPipeline,
    { $count: 'total' }
  ]);

  const total = totalDocs.length > 0 ? totalDocs[0].total : 0;

  return {
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    },
    bookings: await transformTranslatedFields(bookings, language.code)
  };
};

const getTempleId = async (userId, role) => {
  if (role === userTypeValue.TEMPLE_ADMIN) {
    const templeAdmin = await TempleAdmin.findById(userId);

    if (!templeAdmin) {
      throwBadRequestError('User is not a temple admin');
    }

    return templeAdmin.temple;
  } else if (role === userTypeValue.KIOSK) {
    const kioskUser = await KioskUser.findById(userId)
      .select('-password')
      .populate('kiosk');

    if (!kioskUser) {
      throwBadRequestError('Kiosk user not found');
    }

    return kioskUser.kiosk.temple;
  } else {
    throwBadRequestError('Invalid user role');
  }
};

//* 4. Function to get temple bookings
const getTempleBookings = async (userId, queryParams, role) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, status, date, startDate, endDate, startTime, endTime, search, type, event, poojaSchedule, darshanSchedule, isPujariAssigned } = queryParams;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  // Check if the user is a temple admin
  const templeId = await getTempleId(userId, role);

  // Build base filter object
  const filter = { temple: new mongoose.Types.ObjectId(templeId), status: { $nin: [ 'REFUNDED', 'REFUND_INITIATED', 'CANCELLED' ] } };

  // Add status filter if provided
  if (status) {
    const statusArray = status.split(',').map(s => s.trim().toUpperCase());

    filter.status = { $in: statusArray };
  }

  if (event) {
    filter.event = new mongoose.Types.ObjectId(event);
  }

  if (poojaSchedule) {
    filter.poojaSchedule = new mongoose.Types.ObjectId(poojaSchedule);
  }

  if (darshanSchedule) {
    filter.darshanSchedule = new mongoose.Types.ObjectId(darshanSchedule);
  }

  // Add type filter if provided
  if (type) {
    filter.type = type;
  } else {
    if (role === userTypeValue.KIOSK) {
      filter.type = { $ne: 'EVENT' };
    }
  }

  if (isPujariAssigned !== undefined) {
    if (isPujariAssigned) {
      filter.assignedPujari = { $ne: null };
    } else {
      filter.assignedPujari = { $eq: null };
    }
  }

  // Handle date filtering
  if (date || startDate || endDate) {
    const dateFilter = {};

    if (date) {
      // For single date, we want to match exactly this date
      const exactDate = new Date(date);

      // exactDate.setUTCHours(0, 0, 0, 0);

      // Create a date range for the entire day
      const startOfDay = new Date(exactDate);
      const endOfDay = new Date(exactDate);

      endOfDay.setUTCHours(23, 59, 59, 999);

      dateFilter.$gte = startOfDay;
      dateFilter.$lte = endOfDay;
    } else {
      // For date range
      if (startDate) {
        const start = new Date(startDate);

        start.setUTCHours(0, 0, 0, 0);
        dateFilter.$gte = start;
      }
      if (endDate) {
        const end = new Date(endDate);

        end.setUTCHours(23, 59, 59, 999);
        dateFilter.$lte = end;
      }
    }

    //* Create an OR condition to match either regular date or event dates
    filter.$or = [
      //* For darshan/pooja bookings
      { date: dateFilter },

      //* For event bookings
      {
        'eventDates': {
          $elemMatch: {
            'date': dateFilter
          }
        }
      }
    ];
  }

  // Handle time slot filtering
  if (startTime || endTime) {
    const timeFilter = {};

    if (startTime) {
      timeFilter['timeSlot.startTime'] = { $gte: startTime };
    }

    if (endTime) {
      timeFilter['timeSlot.endTime'] = { $lte: endTime };
    }

    // Add time filter to the main filter
    // If we already have date filters with $or, we need to combine them properly
    if (filter.$or) {
      // We need to apply time filters to both branches of the $or
      filter.$or = filter.$or.map(condition => {
        // For each condition in the $or array, add the time filter conditions
        return { $and: [ condition, timeFilter ] };
      });
    } else {
      // If no date filter, simply add the time filter directly
      Object.assign(filter, timeFilter);
    }
  }

  // Build aggregation pipeline
  const aggregatePipeline = [
    // Match initial filters
    { $match: filter },

    // Lookup user details
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'user'
      }
    },
    { $unwind: '$user' },

    // Lookup temple details
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'temple'
      }
    },
    { $unwind: '$temple' },

    // Lookup darshan schedule details
    {
      $lookup: {
        from: 'darshanschedules',
        localField: 'darshanSchedule',
        foreignField: '_id',
        as: 'darshanSchedule'
      }
    },
    {
      $unwind: {
        path: '$darshanSchedule',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup pooja schedule details
    {
      $lookup: {
        from: 'poojaschedules',
        localField: 'poojaSchedule',
        foreignField: '_id',
        as: 'poojaSchedule'
      }
    },
    {
      $unwind: {
        path: '$poojaSchedule',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup event details
    {
      $lookup: {
        from: 'events',
        localField: 'event',
        foreignField: '_id',
        as: 'event'
      }
    },
    {
      $unwind: {
        path: '$event',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup assigned pujari details
    {
      $lookup: {
        from: 'pujaris',
        localField: 'assignedPujari',
        foreignField: '_id',
        as: 'pujari'
      }
    },
    {
      $unwind: {
        path: '$pujari',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup pooja recording details
    {
      $lookup: {
        from: 'poojarecordings',
        localField: '_id',
        foreignField: 'booking',
        as: 'poojaRecording'
      }
    },

    // Add search conditions if search parameter is provided
    ...(search ? [{
      $match: {
        $or: [
          { bookingNumber: { $regex: search, $options: 'i' } },
          { [`primaryDevoteeDetails.fullName.${language.code}`]: { $regex: search, $options: 'i' } },
          { 'primaryDevoteeDetails.phoneNumber': { $regex: search, $options: 'i' } },
          { [`user.firstName.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`user.lastName.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`darshanSchedule.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`poojaSchedule.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`poojaSchedule.description.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`event.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`pujari.name.${language.code}`]: { $regex: search, $options: 'i' } }
        ]
      }
    }] : [])
  ];

  // Add sorting
  const sortObject = {};

  // Add computed fields for new sorting options
  aggregatePipeline.push({
    $addFields: {
      // For booking name sorting (pooja schedule name, darshan name, or event name)
      bookingName: {
        $cond: [
          { $eq: [ '$type', 'PHYSICAL_POOJA' ] },
          '$poojaSchedule.name',
          {
            $cond: [
              { $eq: [ '$type', 'VIRTUAL_POOJA' ] },
              '$poojaSchedule.name',
              {
                $cond: [
                  { $eq: [ '$type', 'PHYSICAL_DARSHAN' ] },
                  '$darshanSchedule.name',
                  '$event.name' // For EVENT type
                ]
              }
            ]
          }
        ]
      },
      // For date sorting, we need to handle EVENT and non-EVENT bookings differently
      sortDate: {
        $cond: {
          if: { $eq: [ '$type', 'EVENT' ] },
          then: {
            $cond: {
              if: { $eq: [ parseInt(sortOrder), 1 ] },
              // For ascending order, get the earliest date
              then: { $min: '$eventDates.date' },
              // For descending order, get the latest date
              else: { $max: '$eventDates.date' }
            }
          },
          else: '$date'
        }
      }
    }
  });

  // Set the sort object based on sortBy parameter
  if (sortBy === 'date') {
    sortObject.sortDate = parseInt(sortOrder) || -1;
  } else if (sortBy === 'bookingName') {
    sortObject.bookingName = parseInt(sortOrder) || -1;
  } else if (sortBy === 'type') {
    sortObject.type = parseInt(sortOrder) || -1;
  } else if (sortBy === 'pujari') {
    sortObject['pujari.name'] = parseInt(sortOrder) || -1;
  } else {
    sortObject[sortBy] = parseInt(sortOrder) || -1;
  }

  aggregatePipeline.push({ $sort: sortObject });

  // Remove temporary fields after sorting
  aggregatePipeline.push({
    $project: {
      sortDate: 0,
      bookingName: 0
    }
  });

  // Add pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  aggregatePipeline.push(
    { $skip: skip },
    { $limit: parseInt(limit) }
  );

  // Execute aggregation
  const bookings = await Booking.aggregate(aggregatePipeline).collation({ locale: 'en', strength: 1 });

  // Add isVerified flag and verification counts to each booking
  if (date) {
    const verificationDate = moment(date).format('YYYY-MM-DD');

    bookings.map(booking => {
      let isVerified = false;
      let verifiedCount = 0;
      let nonVerifiedCount = 0;

      // Check primary devotee verification
      if (booking.primaryDevoteeDetails && booking.primaryDevoteeDetails.verification) {
        const verificationMap = booking.primaryDevoteeDetails.verification;

        // Check if any date has verified=true
        const primaryDevoteeVerified = Object.entries(verificationMap).some(([ key, value ]) => key === verificationDate && value.verified);

        isVerified = primaryDevoteeVerified;

        // Increment appropriate counter
        if (primaryDevoteeVerified) {
          verifiedCount++;
        } else {
          nonVerifiedCount++;
        }
      } else if (booking.primaryDevoteeDetails) {
        // Primary devotee exists but no verification
        nonVerifiedCount++;
      }

      // Check other devotees
      if (booking.otherDevotees && booking.otherDevotees.length > 0) {
        booking.otherDevotees.forEach(devotee => {
          let devoteeVerified = false;

          if (devotee.verification) {
            devoteeVerified = Object.entries(devotee.verification).some(([ key, value ]) => key === verificationDate && value.verified);
            if (devoteeVerified) {
              isVerified = true;
              verifiedCount++;
            } else {
              nonVerifiedCount++;
            }
          } else {
            nonVerifiedCount++;
          }
        });
      }

      booking.isVerified = isVerified;
      booking.verifiedCount = verifiedCount;
      booking.nonVerifiedCount = nonVerifiedCount;
    });
  }

  // Get total count for pagination
  const countPipeline = aggregatePipeline.slice(0, -2); // Remove skip and limit
  const totalDocs = await Booking.aggregate([
    ...countPipeline,
    { $count: 'total' }
  ]);

  const total = totalDocs.length > 0 ? totalDocs[0].total : 0;

  return {
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    },
    bookings: await transformTranslatedFields(bookings, language.code)
  };
};

//* 6. Function to get booking by id
const getBookingById = async (bookingId, user) => {
  const booking = await Booking.findById(bookingId)
    .populate('temple user darshanSchedule poojaSchedule assignedPujari event address').lean();

  if (!booking) {
    throwBadRequestError('Booking not found');
  }

  const bookingRecording = await PoojaRecording.findOne({ booking: bookingId }).populate('kioskUser');

  booking.poojaRecording = bookingRecording ?? null;

  let language = { code: 'en' }; // Default to English

  if (user && user.id) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  return await transformTranslatedFields(booking, language.code);

};

//* 7. Function to get darshan slot availability
const getDarshanSlotAvailability = async ({ temple, date, darshanSchedule }) => {

  const templeData = await Temple.findById(temple);

  if (!templeData) {
    throwBadRequestError('Temple not found or inactive');
  }

  //* 2. Get the darshan schedule
  const schedule = await DarshanSchedule.findOne({
    _id: darshanSchedule,
    temple: temple,
    status: 'ACTIVE'
  });

  if (!schedule) {
    throwBadRequestError('Darshan schedule not found or inactive');
  }

  //* 3. Validate date based on schedule type
  validateScheduleDate(date, schedule);

  //* 4. Check slot availability
  // Get all slots with their availability
  const slotsWithAvailability = await Promise.all(schedule.timeSlots.map(async (slot) => {
    // Get all bookings for this slot
    const slotBookings = await Booking.countDocuments({
      temple,
      date: date,
      type: type.PHYSICAL_DARSHAN,
      'timeSlot.startTime': slot.startTime,
      'timeSlot.endTime': slot.endTime,
      status: 'COMPLETED'
    });

    // Calculate available occupancy
    const availableOccupancy = Math.max(0, schedule.occupancyPerSlot - slotBookings);
    const occupancyRatio = availableOccupancy / schedule.occupancyPerSlot;
    const occupancyPerSlot = schedule.occupancyPerSlot;

    // Determine slot status and selectability
    let status;
    let selectable;

    if (availableOccupancy <= 0) {
      status = 'GREY';
      selectable = false;
    } else if (occupancyRatio <= 0.25 && occupancyPerSlot > 10) {
      status = 'RED';
      selectable = true;
    } else if (occupancyRatio <= 0.50 && occupancyPerSlot <= 10) {
      status = 'RED';
      selectable = true;
    } else {
      status = 'GREEN';
      selectable = true;
    }

    return {
      startTime: slot.startTime,
      endTime: slot.endTime,
      totalCapacity: schedule.occupancyPerSlot,
      availableOccupancy,
      isAvailable: availableOccupancy > 0,
      status,
      selectable
    };
  }));

  return {
    temple,
    date,
    scheduleId: schedule._id, // Added schedule ID in response
    scheduleType: schedule.dateType, // Added to show which type of schedule was used
    pricing: schedule.pricing,
    slots: slotsWithAvailability
  };
};

//* 8. Function to get pooja slot availability
const getPoojaSlotAvailability = async ({ temple, date, poojaSchedule }) => {
  const templeData = await Temple.findById(temple);

  if (!templeData) {
    throwBadRequestError('Temple not found or inactive');
  }

  //* 2. Get the darshan schedule
  const schedule = await PoojaSchedule.findOne({
    _id: poojaSchedule,
    temple: temple,
    status: 'ACTIVE'
  });

  if (!schedule) {
    throwBadRequestError('Darshan schedule not found or inactive');
  }

  //* 3. Validate date based on schedule type
  validateScheduleDate(date, schedule);

  //* 4. Check slot availability
  // Get all slots with their availability
  const slotsWithAvailability = await Promise.all(schedule.timeSlots.map(async (slot) => {
    // Get all bookings for this slot
    const slotBookings = await Booking.countDocuments({
      temple,
      date: date,
      type: schedule.type === 'VIRTUAL' ? type.VIRTUAL_POOJA : type.PHYSICAL_POOJA,
      'timeSlot.startTime': slot.startTime,
      'timeSlot.endTime': slot.endTime,
      status: 'COMPLETED'
    });

    // Calculate available occupancy

    const availableOccupancy = Math.max(0, schedule.occupancyPerSlot - slotBookings);
    const occupancyRatio = availableOccupancy / schedule.occupancyPerSlot;
    const occupancyPerSlot = schedule.occupancyPerSlot;

    // Determine slot status and selectability
    let status;
    let selectable;

    if (availableOccupancy <= 0) {
      status = 'GREY';
      selectable = false;
    } else if (occupancyRatio <= 0.25 && occupancyPerSlot > 10) {
      status = 'RED';
      selectable = true;
    } else if (occupancyRatio <= 0.50 && occupancyPerSlot <= 10) {
      status = 'RED';
      selectable = true;
    } else {
      status = 'GREEN';
      selectable = true;
    }

    return {
      startTime: slot.startTime,
      endTime: slot.endTime,
      totalCapacity: schedule.occupancyPerSlot,
      availableOccupancy,
      isAvailable: availableOccupancy > 0,
      status,
      selectable
    };
  }));

  return {
    temple,
    date,
    scheduleId: schedule._id,
    scheduleType: schedule.dateType,
    type: schedule.type,
    duration: schedule.duration,
    pricing: schedule.pricing,
    slots: slotsWithAvailability
  };
};

//* 9. Function to fetch all user bookings
const fetchAllBookings = async (queryParams) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, status, startDate, endDate, type, search, user, temple, date } = queryParams;

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  // Build base filter object
  const filter = {};

  if (date) {
    filter.updatedAt = {
      $gte: moment(date).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(date).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }
  // Add user filter if provided
  if (user) {
    filter.user = new mongoose.Types.ObjectId(user);
  }

  // Add temple filter if provided
  if (temple) {
    filter.temple = new mongoose.Types.ObjectId(temple);
  }

  // Add status filter if provided
  if (status) {
    const statusArray = status.split(',').map(s => s.trim().toUpperCase());

    filter.status = { $in: statusArray };
  }

  // Add type filter if provided
  if (type) {
    filter.type = type;
  }

  // Handle date range filtering
  if (startDate || endDate) {
    const dateFilter = {};

    if (startDate) {
      const start = new Date(startDate);

      start.setUTCHours(0, 0, 0, 0);
      dateFilter.$gte = start;
    }
    if (endDate) {
      const end = new Date(endDate);

      end.setUTCHours(23, 59, 59, 999);
      dateFilter.$lte = end;
    }

    //* Create an OR condition to match either regular date or event dates
    filter.$or = [
      //* For darshan/pooja bookings
      { date: dateFilter },

      //* For event bookings
      {
        'eventDates': {
          $elemMatch: {
            'date': dateFilter
          }
        }
      }
    ];
  }

  // Build aggregation pipeline
  const aggregatePipeline = [
    // Match initial filters
    { $match: filter },

    // Lookup temple details
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'temple'
      }
    },
    { $unwind: '$temple' },

    // Lookup user details
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'user'
      }
    },
    { $unwind: '$user' },

    // Lookup default address
    {
      $lookup: {
        from: 'addresses',
        let: { userId: '$user._id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$userId', '$$userId' ] }, //* Checks that the address belongs to the same user as in the booking
                  { $eq: [ '$isDefault', true ] }, //*  Filters only the default address
                  { $eq: [ '$deletedAt', null ] } //* Ensures soft-deleted addresses are excluded
                ]
              }
            }
          },
          { $limit: 1 }
        ],
        as: 'address'
      }
    },
    {
      $unwind: {
        path: '$address',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup darshan schedule details
    {
      $lookup: {
        from: 'darshanschedules',
        localField: 'darshanSchedule',
        foreignField: '_id',
        as: 'darshanSchedule'
      }
    },
    {
      $unwind: {
        path: '$darshanSchedule',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup pooja schedule details
    {
      $lookup: {
        from: 'poojaschedules',
        localField: 'poojaSchedule',
        foreignField: '_id',
        as: 'poojaSchedule'
      }
    },
    {
      $unwind: {
        path: '$poojaSchedule',
        preserveNullAndEmptyArrays: true
      }
    },

    {
      $lookup: {
        from: 'pujaris',
        localField: 'assignedPujari',
        foreignField: '_id',
        as: 'pujari'
      }
    },
    {
      $unwind: {
        path: '$pujari',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup event details
    {
      $lookup: {
        from: 'events',
        localField: 'event',
        foreignField: '_id',
        as: 'event'
      }
    },
    {
      $unwind: {
        path: '$event',
        preserveNullAndEmptyArrays: true
      }
    },

    // Add search conditions if search parameter is provided
    ...(search ? [{
      $match: {
        $or: [
          { [`temple.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { bookingNumber: { $regex: search.toUpperCase(), $options: 'i' } },
          { [`primaryDevoteeDetails.fullName.${language.code}`]: { $regex: search, $options: 'i' } },
          { 'primaryDevoteeDetails.phoneNumber': { $regex: search, $options: 'i' } },
          { [`user.firstName.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`user.lastName.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`darshanSchedule.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`poojaSchedule.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`event.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`pujari.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`address.addressLine1.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`address.addressLine2.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`address.city.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`address.state.${language.code}`]: { $regex: search, $options: 'i' } },
          { ['address.postalCode']: { $regex: search, $options: 'i' } },
        ]
      }
    }] : [])
  ];

  // Add sorting
  const sortObject = {};

  // Add computed fields for new sorting options
  aggregatePipeline.push({
    $addFields: {
      // For booking name sorting (pooja schedule name, darshan name, or event name)
      bookingName: {
        $cond: [
          { $eq: [ '$type', 'PHYSICAL_POOJA' ] },
          `$poojaSchedule.name.${language.code}`,
          {
            $cond: [
              { $eq: [ '$type', 'VIRTUAL_POOJA' ] },
              `$poojaSchedule.name.${language.code}`,
              {
                $cond: [
                  { $eq: [ '$type', 'PHYSICAL_DARSHAN' ] },
                  `$darshanSchedule.name.${language.code}`,
                  `$event.name.${language.code}` // For EVENT type
                ]
              }
            ]
          }
        ]
      },
      // For date sorting, we need to handle EVENT and non-EVENT bookings differently
      sortDate: {
        $cond: {
          if: { $eq: [ '$type', 'EVENT' ] },
          then: {
            $cond: {
              if: { $eq: [ parseInt(sortOrder), 1 ] },
              // For ascending order, get the earliest date
              then: { $min: '$eventDates.date' },
              // For descending order, get the latest date
              else: { $max: '$eventDates.date' }
            }
          },
          else: '$date'
        }
      }
    }
  });

  // Set the sort object based on sortBy parameter
  if (sortBy === 'date') {
    sortObject.sortDate = parseInt(sortOrder) || -1;
  } else if (sortBy === 'bookingName') {
    sortObject.bookingName = parseInt(sortOrder) || -1;
  } else if (sortBy === 'type') {
    sortObject.type = parseInt(sortOrder) || -1;
  } else if (sortBy === 'pujari') {
    sortObject[`pujari.name.${language.code}`] = parseInt(sortOrder) || -1;
  } else {
    sortObject[sortBy] = parseInt(sortOrder) || -1;
  }

  aggregatePipeline.push({ $sort: sortObject });

  // Remove temporary fields after sorting
  aggregatePipeline.push({
    $project: {
      sortDate: 0,
      bookingName: 0
    }
  });

  // Add pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  aggregatePipeline.push(
    { $skip: skip },
    { $limit: parseInt(limit) }
  );

  // Execute aggregation
  const bookings = await Booking.aggregate(aggregatePipeline).collation({ locale: 'en', strength: 1 });

  // Get total count for pagination
  const countPipeline = aggregatePipeline.slice(0, -2); // Remove skip and limit
  const totalDocs = await Booking.aggregate([
    ...countPipeline,
    { $count: 'total' }
  ]);

  const total = totalDocs.length > 0 ? totalDocs[0].total : 0;

  return {
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    },
    bookings: await transformTranslatedFields(bookings, language.code)
  };
};

//* 10. Funtion to list all the whatsapp numbers of a specific user 
const listWhatsappNumbers = async (userId, queryParams) => {
  const { page = 1, limit = 10, search = '', countryCode } = queryParams;
  const skip = (parseInt(page) - 1) * parseInt(limit);

  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError(messages.USER_NOT_FOUND);
  }

  let whatsappNumbers = user.whatsappNumbers || [];

  // Filter by countryCode
  if (countryCode) {
    whatsappNumbers = whatsappNumbers.filter(
      (num) => num.countryCode?.trim() === countryCode.trim()
    );
  }

  // Apply search
  if (search) {
    const normalizedSearch = search.toLowerCase().replace(/\s+/g, '');

    whatsappNumbers = whatsappNumbers.filter((num) =>
      `${num.countryCode}${num.phoneNumber}`.replace(/\s+/g, '').toLowerCase().includes(normalizedSearch)
    );
  }

  const total = whatsappNumbers.length;
  const paginated = whatsappNumbers.slice(skip, skip + parseInt(limit));

  return {
    whatsappNumbers: paginated.map((num) => ({
      countryCode: num.countryCode,
      phoneNumber: num.phoneNumber,
      formattedNumber: `${num.countryCode} ${num.phoneNumber}`,
      isPrimaryWhatsappNumber: num.isPrimaryWhatsappNumber
    })),
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      limit: parseInt(limit)
    }
  };
};

//* Function to submit feedback 
const submitFeedback = async (bookingId, userId, body) => {
  // De-structure body 
  const { rating, feedback } = body;

  // Check user exist 
  const user = await User.findById(userId);

  if (!user) {
    throwNotFoundError(messages.USER_NOT_FOUND);
  }

  // Check booking exist 
  const booking = await Booking.findById(bookingId);

  if (!booking) {
    throwNotFoundError(user?.preferredLanguage === 'English' ? messages.BOOKING_NOT_FOUND : messages.BOOKING_NOT_FOUND_HI);
  }

  if (booking.status !== status.COMPLETED) {
    throwBadRequestError(user?.preferredLanguage === 'English' ? messages.BOOKING_NOT_COMPLETED : messages.BOOKING_NOT_COMPLETED_HI);
  }

  if (booking.isCompleted !== true || booking.isCompleted !== 'true') {
    throwBadRequestError(user?.preferredLanguage === 'English' ? messages.BOOKING_NOT_COMPLETED : messages.BOOKING_NOT_COMPLETED_HI);
  }

  if (booking.type !== type.PHYSICAL_POOJA && booking.type !== type.VIRTUAL_POOJA) {
    throwBadRequestError(user?.preferredLanguage === 'English' ? messages.FEEDBACK_NOT_ALLOWED : messages.FEEDBACK_NOT_ALLOWED_HI);
  }

  if (booking?.rating !== null) {
    throwBadRequestError(user?.preferredLanguage === 'English' ? messages.FEEDBACK_ALREADY_SUBMITTED : messages.FEEDBACK_ALREADY_SUBMITTED_HI);
  }

  return await Booking.findByIdAndUpdate(
    bookingId,
    {
      rating, 
      feedback: feedback || ''
    },
    { new: true }
  );
};

module.exports = {
  createDarshanBooking,
  createPoojaBooking,
  createEventBooking,
  getMyBookings,
  getTempleBookings,
  getBookingById,
  getDarshanSlotAvailability,
  getPoojaSlotAvailability,
  fetchAllBookings,
  cancelBooking,
  listWhatsappNumbers,
  submitFeedback
};

