const PoojaSchedule = require('../../../models/PoojaSchedule');
const Temple = require('../../../models/Temple');
const { throwBadRequestError } = require('../../../errors');
const { default: mongoose } = require('mongoose');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');
const states = require('../../../../data/states.json');
const moment = require('moment');
const { generateSKU } = require('../../../utils/skuGenerator');

const createPoojaSchedule = async (scheduleData, adminId) => {
  // Check if temple exists
  const temple = await Temple.findById(scheduleData.temple);

  if (!temple) {
    throwBadRequestError('Temple not found');
  }

  if (scheduleData.type === 'VIRTUAL') {
    const threeHoursFromNow = moment().add(3, 'hours').format('YYYY-MM-DD HH:mm:ss');
    const sixMonthsFromNow = moment().add(6, 'months').format('YYYY-MM-DD');

    if (scheduleData.dateType === 'SPECIFIC_DATE') {

      let dateSlot;
      const specificDate = moment(scheduleData.specificDate).format('YYYY-MM-DD');

      scheduleData.timeSlots.forEach(slot => {
        dateSlot = moment(`${specificDate} ${slot.startTime}`, 'YYYY-MM-DD HH:mm A');

        if (dateSlot.isBefore(threeHoursFromNow)) {
          throwBadRequestError('Each date time slot must be at least 3 hours from now');
        }

        if (dateSlot.isAfter(sixMonthsFromNow)) {
          throwBadRequestError('Each date time slot must be before 6 months from now');
        }
      });

    } else if (scheduleData.dateType === 'DATE_RANGE') {
      let dateSlot;
      const startDate = moment(scheduleData.dateRange.startDate).format('YYYY-MM-DD');

      scheduleData.timeSlots.forEach(slot => {
        dateSlot = moment(`${startDate} ${slot.startTime}`, 'YYYY-MM-DD HH:mm A');

        if (dateSlot.isBefore(threeHoursFromNow)) {
          throwBadRequestError('Each date time slot must be at least 3 hours from now');
        }

        if (dateSlot.isAfter(sixMonthsFromNow)) {
          throwBadRequestError('Each date time slot must be before 6 months from now');
        }
      });
    }
  }

  if (scheduleData.type === 'PHYSICAL') {
    const fiveDaysFromToday = moment().add(5, 'days').format('YYYY-MM-DD');
    const sixMonthsFromNow = moment().add(6, 'months').format('YYYY-MM-DD');

    if (scheduleData.dateType === 'SPECIFIC_DATE') {
      const specificDate = moment(scheduleData.specificDate);

      if (specificDate.isBefore(fiveDaysFromToday)) {
        throwBadRequestError('Date must be at least 5 days from now');
      }

      if (specificDate.isAfter(sixMonthsFromNow)) {
        throwBadRequestError('Date must be before 6 months from now');
      }

    } else if (scheduleData.dateType === 'DATE_RANGE') {
      const startDate = moment(scheduleData.dateRange.startDate);

      if (startDate.isBefore(fiveDaysFromToday)) {
        throwBadRequestError('Start date must be at least 5 days from now');
      }

      if (startDate.isAfter(sixMonthsFromNow)) {
        throwBadRequestError('Start date must be before 6 months from now');
      }
    }
  }

  // Translate fields
  const translatedFields = [ 'name', 'description', 'guideline' ];
  const translatedData = await translateDataForStore(translatedFields, scheduleData);

  translatedFields.forEach(field => {
    if (scheduleData[field]) {
      delete scheduleData[field];
    }
  });

  let code = '0001';

  const state = states.find(state => state.name === temple.state.en);
  const stateCode = state ? state.id : 'XX';

  if (temple.lastServiceCode) {
    code = `${(parseInt(temple.lastServiceCode || 0) + 1).toString().padStart(4, '0')}`;
  }

  const codeType = scheduleData.type === 'PHYSICAL' ? 'PP' : 'VP';

  scheduleData.serviceCode = `S-${stateCode}-${temple.templeCode}-${codeType}-${code}`;

  temple.lastServiceCode = code;
  await temple.save();

  if (scheduleData.promotionalKit) {
    let kitCode = '0001';

    if (temple.lastKitCode) {
      kitCode = `${(parseInt(temple.lastKitCode || 0) + 1).toString().padStart(4, '0')}`;
    }
    temple.lastKitCode = kitCode;
    await temple.save();
    scheduleData.promotionalKit.productCode = `S-${stateCode}-${temple.templeCode}-PB-${kitCode}`;
  }

  scheduleData = {
    ...scheduleData,
    ...translatedData
  };

  if (!scheduleData.sku) {
    scheduleData.sku = generateSKU('S', scheduleData.name?.en || scheduleData.name?.hi || scheduleData.name);
  }

  // Create pooja schedule
  const poojaSchedule = await PoojaSchedule.create({
    ...scheduleData,
    createdBy: adminId,
    updatedBy: adminId
  });

  return poojaSchedule.populate([
    { path: 'temple', select: 'name location' },
    { path: 'createdBy', select: 'name email' },
    { path: 'updatedBy', select: 'name email' }
  ]);
};

const updatePoojaSchedule = async (scheduleId, updateData, adminId) => {
  const schedule = await PoojaSchedule.findById(scheduleId);
  
  if (!schedule) {
    throwBadRequestError('Puja schedule not found');
  }

  // If temple is being updated, verify it exists
  if (updateData.temple) {
    const temple = await Temple.findById(updateData.temple);

    if (!temple) {
      throwBadRequestError('Temple not found');
    }
  }
  
  const scheduleType = updateData.type || schedule.type;
  const dateType = updateData.dateType || schedule.dateType;
  const specificDate = updateData.specificDate || schedule.specificDate;
  const slots = updateData.timeSlots || schedule.timeSlots;

  if (scheduleType === 'VIRTUAL') {
    const threeHoursFromNow = moment().add(3, 'hours').format('YYYY-MM-DD HH:mm:ss');
    const sixMonthsFromNow = moment().add(6, 'months').format('YYYY-MM-DD');

    if (dateType === 'SPECIFIC_DATE') {

      let dateSlot;
      const currentSpecificDate = moment(specificDate).format('YYYY-MM-DD');

      slots.forEach(slot => {
        dateSlot = moment(`${currentSpecificDate} ${slot.startTime}`, 'YYYY-MM-DD HH:mm A');

        if (dateSlot.isBefore(threeHoursFromNow)) {
          throwBadRequestError('Each date time slot must be at least 3 hours from now');
        }

        if (dateSlot.isAfter(sixMonthsFromNow)) {
          throwBadRequestError('Each date time slot must be before 6 months from now');
        }
      });

    } else if (dateType === 'DATE_RANGE') {
      let dateSlot;
      const dateRangeStart = updateData.dateRange.startDate || schedule.dateRange.startDate;
      const startDate = moment(dateRangeStart).format('YYYY-MM-DD');

      slots.forEach(slot => {
        dateSlot = moment(`${startDate} ${slot.startTime}`, 'YYYY-MM-DD HH:mm A');

        if (dateSlot.isBefore(threeHoursFromNow)) {
          throwBadRequestError('Each date time slot must be at least 3 hours from now');
        }

        if (dateSlot.isAfter(sixMonthsFromNow)) {
          throwBadRequestError('Each date time slot must be before 6 months from now');
        }
      });
    }
  }

  if (scheduleType === 'PHYSICAL') {
    const fiveDaysFromToday = moment().add(5, 'days').format('YYYY-MM-DD');
    const sixMonthsFromNow = moment().add(6, 'months').format('YYYY-MM-DD');

    if (dateType === 'SPECIFIC_DATE') {
      const currentSpecificDate = moment(specificDate);

      if (currentSpecificDate.isBefore(fiveDaysFromToday)) {
        throwBadRequestError('Date must be at least 5 days from now');
      }

      if (currentSpecificDate.isAfter(sixMonthsFromNow)) {
        throwBadRequestError('Date must be before 6 months from now');
      }

    } else if (dateType === 'DATE_RANGE') {
      const dateRangeStart = updateData.dateRange.startDate || schedule.dateRange.startDate;
      const startDate = moment(dateRangeStart);

      if (startDate.isBefore(fiveDaysFromToday)) {
        throwBadRequestError('Start date must be at least 5 days from now');
      }

      if (startDate.isAfter(sixMonthsFromNow)) {
        throwBadRequestError('Start date must be before 6 months from now');
      }
    }
  }

  if (updateData.promotionalKit) {
    if (updateData.promotionalKit && Object.keys(updateData.promotionalKit).length > 0) {
      updateData.promotionalKit = {
        ...schedule.promotionalKit,
        ...updateData.promotionalKit
      };
    } else {
      updateData.promotionalKit = null;
    }
  }

  // Translate fields if provided
  const translatedFields = [ 'name', 'description', 'guideline' ];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  // Update the schedule
  const updatedSchedule = await PoojaSchedule.findByIdAndUpdate(
    scheduleId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  ).populate([
    { path: 'temple', select: 'name location' },
    { path: 'createdBy', select: 'name email' },
    { path: 'updatedBy', select: 'name email' }
  ]);

  return updatedSchedule;
};

const deletePoojaSchedule = async (scheduleId) => {
  const schedule = await PoojaSchedule.findById(scheduleId);
  
  if (!schedule) {
    throwBadRequestError('Puja schedule not found');
  }

  if (schedule.deletedAt || schedule.status === 'INACTIVE') {
    throwBadRequestError('Puja schedule already deleted or inactive');
  }

  //* Soft delete puja schedule 
  schedule.deletedAt = new Date();
  schedule.status = 'INACTIVE';
  await schedule.save();

  return schedule;
};

const listPoojaSchedules = async (query = {}) => {
  const { 
    page = 1, 
    limit = 10, 
    search,
    type,
    temple, 
    status,
    dateType,
    startDate,
    endDate,
    specificDate,
    sortBy = 'createdAt', 
    sortOrder = -1,
    date
  } = query;

  const filter = {
    deletedAt: null
  };

  if (date) {
    filter.updatedAt = {
      $gte: moment(date).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(date).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  if (temple) {
    filter.temple = new mongoose.Types.ObjectId(temple);
  }

  if (status) {
    filter.status = status;
  }

  if (type) {
    filter.type = type;
  }

  if (dateType) {
    filter.dateType = dateType;
  }

  if (startDate) {
    filter['dateRange.startDate'] = { $gte: new Date(startDate) };
  }

  if (endDate) {
    filter['dateRange.endDate'] = { $lte: new Date(endDate) };
  }

  if (specificDate) {
    filter.specificDate = new Date(specificDate);
  }

  const aggregatePipeline = [{
    $lookup: {
      from: 'temples',
      localField: 'temple',
      foreignField: '_id',
      as: 'templeDetails'
    }
  },
  {
    $unwind: { path: '$templeDetails', preserveNullAndEmptyArrays: true }
  },
  {
    $lookup: {
      from: 'admins',
      localField: 'createdBy',
      foreignField: '_id',
      as: 'createdBy'
    }
  },
  {
    $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true }
  },
  {
    $lookup: {
      from: 'admins',
      localField: 'updatedBy',
      foreignField: '_id',
      as: 'updatedBy'
    }
  },
  {
    $unwind: {
      path: '$updatedBy',
      preserveNullAndEmptyArrays: true
    }
  }];

  const matchConditions = {
    $and: [
      { ...filter }
    ]
  };

  const language = { code: 'en' };

  if (search) {
    matchConditions.$and.push({ $or: [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`templeDetails.name.${language.code}`]: { $regex: search, $options: 'i' } },
    ] });
  }

  aggregatePipeline.push({ $match: matchConditions });

  const sortMongoOrder = parseInt(sortOrder) || -1; // Ensure sortOrder is a number
  
  if (sortBy === 'temple') {
    aggregatePipeline.push({
      $sort: {
        [`templeDetails.name.${language.code}`]: sortMongoOrder
      }
    });
  } else {
    aggregatePipeline.push({
      $sort: {
        [sortBy]: sortMongoOrder
      }
    });
  }

  aggregatePipeline.push({
    $facet: {
      data: [
        { $skip: (parseInt(page) - 1) * parseInt(limit) },
        { $limit: parseInt(limit) },
        {
          $project: {
            _id: 1,
            name: 1,
            description: 1,
            dateType: 1,
            specificDate: 1,
            dateRange: 1,
            type: 1,
            timeSlots: 1,
            pricing: 1,
            promotionalKit: 1,
            occupancyPerSlot: 1,
            guideline: 1,
            status: 1,
            createdAt: 1,
            updatedAt: 1,
            temple: {
              _id: '$templeDetails._id',
              name: '$templeDetails.name',
              location: '$templeDetails.location'
            },
            createdBy: {
              _id: '$createdBy._id',
              name: '$createdBy.name',
              email: '$createdBy.email'
            },
            updatedBy: {
              _id: '$updatedBy._id',
              name: '$updatedBy.name',
              email: '$updatedBy.email'
            }
          }
        }
      ],
      total: [{ $count: 'count' }]
    }
  });

  const [ result ] = await PoojaSchedule.aggregate(aggregatePipeline).collation({ locale: 'en', strength: 1 });

  return {
    schedules: await transformTranslatedFields(result.data, language.code),
    pagination: {
      total: result.total[0]?.count || 0,
      page: parseInt(page),
      pages: Math.ceil((result.total[0]?.count || 0) / limit)
    }
  };
};

const getPoojaScheduleById = async (scheduleId) => {
  const schedule = await PoojaSchedule.findById(scheduleId)
    .populate([
      { path: 'temple', select: 'name location' },
      { path: 'createdBy', select: 'name email' },
      { path: 'updatedBy', select: 'name email' }
    ]).lean();

  if (!schedule) {
    throwBadRequestError('Puja schedule not found');
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(schedule, language.code);
};

module.exports = {
  createPoojaSchedule,
  updatePoojaSchedule,
  deletePoojaSchedule,
  listPoojaSchedules,
  getPoojaScheduleById
};
