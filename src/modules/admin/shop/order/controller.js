const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { messages } = require('../../../../messages');
const orderService = require('./service');
const { updateOrderStatusSchema, refundOrderSchema, shipOrderSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../../utils/auditLogger');
const { auditLogAction } = require('../../../../constants/dbEnums');

/**
 * Get all orders with pagination and filters
 */
const getAllOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search, startDate, endDate, sortBy = 'createdAt', sortOrder = -1, date } = req.query;
    
    const data = await orderService.getAllOrders({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search,
      startDate,
      endDate,
      sortBy,
      sortOrder,
      date
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get orders dashboard data
 */
const getOrdersDashboard = async (req, res) => {
  try {
    const data = await orderService.getOrdersDashboard();

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get order by ID
 */
const getOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await orderService.getOrderById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update order status
 */
const updateOrderStatus = async (req, res) => {
  try {
    const { error } = updateOrderStatusSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { id } = req.params;
    const { status, notes } = req.body;
    
    const data = await orderService.updateOrderStatus({
      orderId: id,
      status,
      notes
    });

    //* Save audit log 
    const detail = `Order ${id} status updated to ${status}`;
    const model = 'Order';

    await saveAuditLog(req, req.user.id, auditLogAction.ORDER_STATUS_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ORDER_STATUS_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Refund order
 */
const refundOrder = async (req, res) => {
  try {
    const { error } = refundOrderSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { id } = req.params;
    const { amount, reason } = req.body;
    
    const data = await orderService.refundOrder({
      orderId: id,
      amount,
      reason
    });

    //* Save audit log 
    const detail = `Order ${id} refunded successfully`;
    const model = 'Order';

    await saveAuditLog(req, req.user.id, auditLogAction.ORDER_REFUNDED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ORDER_REFUNDED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Ship order
 */
const shipOrder = async (req, res) => {
  try {
    const { error } = shipOrderSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { id } = req.params;
    const { trackingNumber, shippingProvider, estimatedDelivery } = req.body;
    
    const data = await orderService.shipOrder({
      orderId: id,
      trackingNumber,
      shippingProvider,
      estimatedDelivery
    });

    //* Save audit log 
    const detail = `Order ${id} shipped successfully`;
    const model = 'Order';

    await saveAuditLog(req, req.user.id, auditLogAction.ORDER_SHIPPED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ORDER_SHIPPED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllOrders,
  getOrdersDashboard,
  getOrderById,
  updateOrderStatus,
  refundOrder,
  shipOrder
};
