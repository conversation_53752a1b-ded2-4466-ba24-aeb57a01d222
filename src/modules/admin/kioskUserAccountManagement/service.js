const KioskUser = require('../../../models/KioskUser');
const Token = require('../../../models/Token');
const { throwBadRequestError } = require('../../../errors');
const { userTypeValue } = require('../../../constants/dbEnums');

//* 1. Function to update kiosk user account status 
const updateKioskUserAccountStatus = async (userId, status) => {
  const kioskUser = await KioskUser.findById(userId);

  if (!kioskUser) {
    throwBadRequestError('Kiosk user not found');
  }

  if (status === 'ACTIVATE') {
    
    //* Check if user is already active
    if (kioskUser.deletedAt === null) {
      throwBadRequestError('Kiosk user is already active');
    }

    kioskUser.deletedAt = null;

  } else if (status === 'DEACTIVATE') {
    
    //* Check if user is already deactivated
    if (kioskUser.deletedAt) {
      throwBadRequestError('Kiosk user is already deactivated');
    }

    kioskUser.deletedAt = new Date();
    kioskUser.status = 'INACTIVE';

    //* Also delete all tokens for this kiosk user 
    await Token.deleteMany({ 
      userId: kioskUser._id,
      userType: userTypeValue.KIOSK
    });
  }

  await kioskUser.save();

  return kioskUser;
};

module.exports = {
  updateKioskUserAccountStatus
};