const Bill = require('../../../models/Bill');
const { billStatusValue, billTypeValue, itemTypeValue, paymentGateway } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const Temple = require('../../../models/Temple');
const Vendor = require('../../../models/Vendor');
const Admin = require('../../../models/Admin');
const Address = require('../../../models/Address');
const { generateBillNumber } = require('../../../utils/generateBillNumber');
const moment = require('moment');
const PayPhiTransaction = require('../../../models/PayPhiTransaction');
const TransactionBooking = require('../../../models/TransactionBooking');
const Transaction = require('../../../models/Transaction');

/**
 * Create bill for temple service booking
 * @param {Object} bookingData - Booking data
 * @param {Object} serviceData - Service data (PoojaSchedule or DarshanSchedule)
 * @param {Object} userData - User data
 * @returns {Promise<Object>} Created bill
 */
const createTempleServiceBill = async (bookingData, serviceData, userData) => {
  // Get temple data
  const temple = await Temple.findById(bookingData.temple);

  if (!temple) {
    throw new Error('Temple not found');
  }

  // Get user address for destination of supply
  const userAddress = await Address.findOne({ user: userData._id, deletedAt: null });

  // Determine source of supply (temple state)
  const sourceOfSupply = temple.state?.en || temple.state || 'Unknown';
    
  // Determine destination of supply (user state)
  const destinationOfSupply = userAddress?.state?.en || 'Unknown';

  // Generate bill number
  const billNumber = await generateBillNumber(sourceOfSupply);

  // Calculate due date
  const dueDate = new Date();

  if (temple.paymentTermsInDays) {
    dueDate.setDate(dueDate.getDate() + temple.paymentTermsInDays);
  }

  // Determine pricing based on booking type
  const rate = bookingData?.templePayoutDetails?.totalPayoutSummary?.totalBaseAmount || 0;
  const taxPercentage = bookingData?.templePayoutDetails?.gstPercentage || 0;
  const taxAmount = bookingData?.templePayoutDetails?.totalPayoutSummary?.totalGstAmount || 0;
  const total = bookingData?.templePayoutDetails?.totalPayoutSummary?.totalAmount || 0;
  const itemName = serviceData.name?.en || 'Temple Service';
  const sku = serviceData.sku || serviceData.serviceCode || 'TEMPLE-SERVICE';

  // Get customer name
  const customerName = `${userData.firstName?.en || ''} ${userData.lastName?.en || ''}`.trim() || 'Guest User';

  let transactionId, transactionModel;

  if (bookingData.paymentGateway === paymentGateway.ICICI_BANK) {
    const transaction = await PayPhiTransaction.findOne({ booking: bookingData._id });

    transactionId = transaction._id;
    transactionModel = 'PayPhiTransaction';
  } else {
    const transaction = await TransactionBooking.findOne({ booking: bookingData._id });

    transactionId = transaction._id;
    transactionModel = 'TransactionBooking';
  }

  // Create bill data
  const billData = {
    billNumber,
    billStatus: billStatusValue.OVERDUE,
    type: billTypeValue.TEMPLE,
    sourceOfSupply,
    destinationOfSupply,
    gstIdentificationNumber: temple.gstNumber || 'N/A',
    vendorName: temple.name?.en || 'Temple',
    dueDate,
    currencyCode: 'INR',
    itemName,
    sku,
    itemDescription: serviceData.description?.en || '',
    quantity: 1,
    rate,
    itemType: itemTypeValue.SERVICE,
    taxPercentage,
    taxAmount,
    itemTotal: rate,
    subTotal: rate,
    total,
    balance: total,
    paymentTermsLabel: temple.paymentTermsLabel || '',
    customerName,
    purchaseOrderNumber: bookingData.bookingNumber,
    temple: temple._id,
    pooja: serviceData._id,
    booking: bookingData._id,
    user: userData._id,
    transaction: transactionId,
    transactionModel
  };

  // Create and save bill
  const bill = new Bill(billData);

  await bill.save();

  return bill;
};

/**
 * Create bills for product order items
 * @param {Object} orderData - Order data
 * @param {Array} orderItems - Array of order items
 * @param {Object} userData - User data
 * @returns {Promise<Array>} Array of created bills
 */
const createProductOrderBills = async (orderData, orderItems, userData) => {
  const bills = [];

  // Create a bill for each order item
  for (const item of orderItems) {
    // Get vendor data
    let vendor;
    let vendorName;
    let sourceOfSupply;
    let gstNumber;
    let paymentTermsInDays;
    let paymentTermsLabel;

    if (item.vendorModel === 'Admin') {
      vendor = await Admin.findById(item.vendor);
      vendorName = 'OneGod Admin';
      sourceOfSupply = 'Delhi'; // Default admin location
      gstNumber = process.env.ADMIN_GSTIN || 'N/A';
      paymentTermsInDays = 30; // Default
      paymentTermsLabel = 'Net 30 Days';
    } else {
      vendor = await Vendor.findById(item.vendor);
      if (!vendor) {
        continue;
      }
      vendorName = vendor.businessName?.en || vendor.name?.en || 'Vendor';
      sourceOfSupply = vendor.address?.state || 'Unknown';
      gstNumber = vendor.gstNumber || 'N/A';
      paymentTermsInDays = vendor.paymentTermsInDays || 30;
      paymentTermsLabel = vendor.paymentTermsLabel || 'Net 30 Days';
    }

    // Determine destination of supply (user state)
    const destinationOfSupply = orderData.shippingAddress?.state || 'Unknown';

    // Generate bill number
    const billNumber = await generateBillNumber(sourceOfSupply);

    // Calculate due date
    const dueDate = new Date();

    if (paymentTermsInDays) {
      dueDate.setDate(dueDate.getDate() + paymentTermsInDays);
    }

    let transactionId, transactionModel;

    if (orderData.paymentMethod === paymentGateway.ICICI_BANK) {
      const transaction = await PayPhiTransaction.findOne({ order: orderData._id });

      transactionId = transaction._id;
      transactionModel = 'PayPhiTransaction';
    } else {
      const transaction = await Transaction.findOne({ order: orderData._id });

      transactionId = transaction._id;
      transactionModel = 'Transaction';
    }

    // Get customer name
    const customerName = `${userData.firstName?.en || ''} ${userData.lastName?.en || ''}`.trim() || 'Guest User';

    // Create bill data
    const billData = {
      billNumber,
      billStatus: billStatusValue.OVERDUE,
      type: billTypeValue.PRODUCT,
      sourceOfSupply,
      destinationOfSupply,
      gstIdentificationNumber: gstNumber,
      vendorName,
      dueDate,
      currencyCode: 'INR',
      itemName: item.name?.en || 'Product',
      sku: item.variant?.sku || item.product?.productCode || 'PRODUCT',
      itemDescription: item.variant?.description?.en || item.product?.description?.en || '',
      quantity: item.quantity,
      rate: item.purchaseTaxablePrice,
      itemType: itemTypeValue.GOODS,
      taxPercentage: item.gstPercentage || 0,
      itemTotal: item.purchaseTaxablePrice,
      taxAmount: item.purchaseSubTotalGstPrice,
      subTotal: item.purchaseSubTotalTaxablePrice,
      total: item.purchaseSubtotal,
      balance: item.purchaseSubtotal,
      paymentTermsLabel,
      customerName,
      purchaseOrderNumber: orderData.orderNumber,
      vendor: vendor._id,
      vendorModel: item.vendorModel,
      product: item.product,
      variant: item.variant || null,
      order: orderData._id,
      user: userData._id,
      transaction: transactionId,
      transactionModel
    };

    // Create and save bill
    const bill = new Bill(billData);

    await bill.save();
    bills.push(bill);
  }

  return bills;
};

/**
 * Get all bills with pagination and filters
 */
const getAllBills = async (queryParams) => {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    type,
    status,
    vendor,
    temple,
    startDate,
    endDate,
    search,
    date
  } = queryParams;

  // Build filter object
  const filter = {};

  if (type) {
    filter.type = type;
  }

  if (status) {
    filter.billStatus = status;
  }

  if (vendor && mongoose.Types.ObjectId.isValid(vendor)) {
    filter.vendor = vendor;
  }

  if (temple && mongoose.Types.ObjectId.isValid(temple)) {
    filter.temple = temple;
  }

  if (startDate || endDate) {
    filter.billDate = {};
    if (startDate) {
      filter.billDate.$gte = new Date(startDate);
    }
    if (endDate) {
      filter.billDate.$lte = new Date(endDate);
    }
  }

  if (date) {
    filter.updatedAt = {
      $gte: moment(date).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(date).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  if (search) {
    filter.$or = [
      { billNumber: { $regex: search, $options: 'i' } },
      { customerName: { $regex: search, $options: 'i' } },
      { vendorName: { $regex: search, $options: 'i' } },
      { itemName: { $regex: search, $options: 'i' } },
      { purchaseOrderNumber: { $regex: search, $options: 'i' } }
    ];
  }

  // Build sort object
  const sort = {};

  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Execute query with pagination
  const [ bills, total ] = await Promise.all([
    Bill.find(filter)
      .populate('temple')
      .populate('vendor')
      .populate('product')
      .populate('variant')
      .populate('user')
      .populate('booking')
      .populate('order')
      .populate('transaction')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    Bill.countDocuments(filter)
  ]);

  return {
    bills,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get bill by ID
 */
const getBillById = async (billId) => {
  if (!mongoose.Types.ObjectId.isValid(billId)) {
    throw new Error('Invalid bill ID');
  }

  const bill = await Bill.findById(billId)
    .populate('temple')
    .populate('vendor')
    .populate('product')
    .populate('variant')
    .populate('user')
    .populate('booking')
    .populate('order')
    .lean();

  return bill;
};

/**
 * Get bills by booking ID
 */
const getBillsByBookingId = async (booking) => {
  if (!mongoose.Types.ObjectId.isValid(booking)) {
    throw new Error('Invalid booking ID');
  }

  const bills = await Bill.find({ booking })
    .populate('temple')
    .populate('pooja')
    .populate('user')
    .sort({ createdAt: -1 })
    .lean();

  return bills;
};

/**
 * Get bills by order ID
 */
const getBillsByOrderId = async (order) => {
  if (!mongoose.Types.ObjectId.isValid(order)) {
    throw new Error('Invalid order ID');
  }

  const bills = await Bill.find({ order })
    .populate('vendor')
    .populate('product')
    .populate('variant')
    .populate('user')
    .sort({ createdAt: -1 })
    .lean();

  return bills;
};

/**
 * Update bill status
 */
const updateBillStatus = async (billId, status) => {
  if (!mongoose.Types.ObjectId.isValid(billId)) {
    throw new Error('Invalid bill ID');
  }

  if (!Object.values(billStatusValue).includes(status)) {
    throw new Error('Invalid bill status');
  }

  const updatedBill = await Bill.findByIdAndUpdate(
    billId,
    { billStatus: status },
    { new: true }
  )
    .populate('temple')
    .populate('vendor')
    .populate('user')
    .lean();

  if (!updatedBill) {
    throw new Error('Bill not found');
  }

  return updatedBill;
};

/**
 * Get bills summary/statistics
 */
const getBillsSummary = async (queryParams) => {
  const { startDate, endDate, type, vendor, temple } = queryParams;

  // Build filter for date range
  const dateFilter = {};

  if (startDate || endDate) {
    dateFilter.billDate = {};
    if (startDate) {
      dateFilter.billDate.$gte = new Date(startDate);
    }
    if (endDate) {
      dateFilter.billDate.$lte = new Date(endDate);
    }
  }

  // Build additional filters
  const additionalFilters = {};

  if (type) {
    additionalFilters.type = type;
  }
  if (vendor && mongoose.Types.ObjectId.isValid(vendor)) {
    additionalFilters.vendor = vendor;
  }
  if (temple && mongoose.Types.ObjectId.isValid(temple)) {
    additionalFilters.temple = temple;
  }

  const baseFilter = { ...dateFilter, ...additionalFilters };

  // Get summary statistics
  const [
    totalBills,
    pendingBills,
    generatedBills,
    overdueBills,
    totalAmount,
    templeBills,
    productBills
  ] = await Promise.all([
    Bill.countDocuments(baseFilter),
    Bill.countDocuments({ ...baseFilter, billStatus: billStatusValue.PENDING }),
    Bill.countDocuments({ ...baseFilter, billStatus: billStatusValue.GENERATED }),
    Bill.countDocuments({ ...baseFilter, billStatus: billStatusValue.OVERDUE }),
    Bill.aggregate([
      { $match: baseFilter },
      { $group: { _id: null, total: { $sum: '$total' } } }
    ]),
    Bill.countDocuments({ ...baseFilter, type: billTypeValue.TEMPLE }),
    Bill.countDocuments({ ...baseFilter, type: billTypeValue.PRODUCT })
  ]);

  return {
    totalBills,
    billsByStatus: {
      pending: pendingBills,
      generated: generatedBills,
      overdue: overdueBills
    },
    billsByType: {
      temple: templeBills,
      product: productBills
    },
    totalAmount: totalAmount[0]?.total || 0
  };
};

module.exports = {
  createTempleServiceBill,
  createProductOrderBills,
  getAllBills,
  getBillById,
  getBillsByBookingId,
  getBillsByOrderId,
  updateBillStatus,
  getBillsSummary
};
