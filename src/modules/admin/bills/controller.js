const billService = require('./service');
const { throwBadRequestError } = require('../../../errors');
const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { getBillsQuerySchema } = require('./validation');

/**
 * Get all bills with pagination and filters
 */
const getAllBills = async (req, res) => {
  try {
    const { error } = getBillsQuerySchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }
    
    const result = await billService.getAllBills(req.query);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Bills retrieved successfully',
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get bill by ID
 */
const getBillById = async (req, res) => {
  try {
    const { billId } = req.params;
    
    if (!billId) {
      throwBadRequestError('Bill ID is required');
    }

    const bill = await billService.getBillById(billId);
    
    if (!bill) {
      return res.status(404).json({
        success: false,
        message: 'Bill not found'
      });
    }

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Bills retrieved successfully',
      data: bill
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get bills by booking ID
 */
const getBillsByBookingId = async (req, res) => {
  try {
    const { bookingId } = req.params;
    
    if (!bookingId) {
      throwBadRequestError('Booking ID is required');
    }

    const bills = await billService.getBillsByBookingId(bookingId);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Bills retrieved successfully',
      data: bills
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get bills by order ID
 */
const getBillsByOrderId = async (req, res) => {
  try {
    const { orderId } = req.params;
    
    if (!orderId) {
      throwBadRequestError('Order ID is required');
    }

    const bills = await billService.getBillsByOrderId(orderId);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Bills retrieved successfully',
      data: bills
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update bill status
 */
const updateBillStatus = async (req, res) => {
  try {
    const { billId } = req.params;
    const { status } = req.body;
    
    if (!billId) {
      throwBadRequestError('Bill ID is required');
    }
    
    if (!status) {
      throwBadRequestError('Status is required');
    }

    const updatedBill = await billService.updateBillStatus(billId, status);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Bill status updated successfully',
      data: updatedBill
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get bills summary/statistics
 */
const getBillsSummary = async (req, res) => {
  try {
    const summary = await billService.getBillsSummary(req.query);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Bills retrieved successfully',
      data: summary
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllBills,
  getBillById,
  getBillsByBookingId,
  getBillsByOrderId,
  updateBillStatus,
  getBillsSummary
};
