const joi = require('joi');
const { billStatusValue, billTypeValue } = require('../../../constants/dbEnums');

const updateBillStatusSchema = joi.object({
  status: joi.string()
    .valid(...Object.values(billStatusValue))
    .required()
    .messages({
      'any.only': 'Status must be one of: PENDING, GENERATED, OVERDUE',
      'any.required': 'Status is required'
    })
});

const getBillsQuerySchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  sortBy: joi.string().valid('createdAt', 'billDate', 'dueDate', 'total', 'billNumber').default('createdAt'),
  sortOrder: joi.number().valid(1, -1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  type: joi.string().valid(...Object.values(billTypeValue)),
  status: joi.string().valid(...Object.values(billStatusValue)),
  vendor: joi.string().pattern(/^[0-9a-fA-F]{24}$/),
  temple: joi.string().pattern(/^[0-9a-fA-F]{24}$/),
  startDate: joi.date().iso().allow('').optional(),
  endDate: joi.date().iso().min(joi.ref('startDate')),
  search: joi.string().min(1).max(100),
  date: joi.date().iso().allow('').optional()
});

const getBillsSummaryQuerySchema = joi.object({
  startDate: joi.date().iso(),
  endDate: joi.date().iso().min(joi.ref('startDate')),
  type: joi.string().valid(...Object.values(billTypeValue)),
  vendor: joi.string().pattern(/^[0-9a-fA-F]{24}$/),
  temple: joi.string().pattern(/^[0-9a-fA-F]{24}$/)
});

module.exports = {
  updateBillStatusSchema,
  getBillsQuerySchema,
  getBillsSummaryQuerySchema
};
