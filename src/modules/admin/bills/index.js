const express = require('express');
const router = express.Router();
const billController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

// Apply admin authentication to all routes
router.use(auth, isAdminOrSuperAdmin);

router.get('/', billController.getAllBills);
router.get('/summary', billController.getBillsSummary);
router.get('/booking/:bookingId', billController.getBillsByBookingId);
router.get('/order/:orderId', billController.getBillsByOrderId);
router.get('/:billId', billController.getBillById);
router.put('/:billId/status', billController.updateBillStatus);

module.exports = router;
